doctrine:
    dbal:
        # database connection options (get from .env)
        driver: '%env(DATABASE_DRIVER)%'
        host: '%env(DATABASE_HOST)%'
        port: '%env(DATABASE_PORT)%'
        dbname: '%env(DATABASE_NAME)%'
        user: '%env(resolve:DATABASE_USERNAME)%'
        password: '%env(resolve:DATABASE_PASSWORD)%'

        # server configuration
        charset: utf8mb4
        server_version: '%env(DATABASE_VERSION)%'
        profiling_collect_backtrace: '%kernel.debug%'
    orm:
        auto_generate_proxy_classes: true
        enable_lazy_ghost_objects: true
        report_fields_where_declared: true
        validate_xml_mapping: true
        naming_strategy: doctrine.orm.naming_strategy.underscore_number_aware
        auto_mapping: false
        controller_resolver:
            auto_mapping: false
        mappings:
            App:
                is_bundle: false
                dir: '%kernel.project_dir%/src/Entity'
                prefix: 'App\Entity'
                alias: App
