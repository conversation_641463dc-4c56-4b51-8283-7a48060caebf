parameters:
    pwa_app_support: '%env(PWA_APP_SUPPORT)%'

services:
    _defaults:
        autowire: true      # automatically injects dependencies
        autoconfigure: true # automatically register services

    # define autowiring services for App namespace
    App\:
        resource: '../src/'
        exclude:
            - '../src/Entity/'
            - '../src/Kernel.php'

    # exception event subscriber (error handling functionality)
    App\Event\Subscriber\ExceptionEventSubscriber:
        tags:
            - { name: kernel.event_subscriber }

    # twig extensions
    # --------------------------------------------------------------------------------- #
    # linkify extension
    App\Twig\LinkifyExtension:
        tags:
            - { name: 'twig.extension' }

    # auth manager extension
    App\Twig\AuthManagerExtension:
        arguments:
            $authManager: '@App\Manager\AuthManager'
        tags: [ 'twig.extension' ]

    # todo manager extension
    App\Twig\TodoManagerExtension:
        arguments:
            $todoManager: '@App\Manager\TodoManager'
        tags: [ 'twig.extension' ]

    # request middlewares
    # --------------------------------------------------------------------------------- #
    # check if assets is builded
    App\Middleware\AssetsCheckMiddleware:
        tags:
            - { name: 'kernel.event_listener', event: 'kernel.request', priority: 10 }

    # check if app is running on linux
    App\Middleware\LinuxCheckMiddleware:
        tags:
            - { name: kernel.event_listener, event: kernel.request, priority: 9 }

    # escape request data (for security)
    App\Middleware\EscapeRequestDataMiddleware:
        tags:
            - { name: 'kernel.event_listener', event: 'kernel.request', priority: 8 }

    # check if database is online
    App\Middleware\DatabaseOnlineMiddleware:
        tags:
            - { name: 'kernel.event_listener', event: 'kernel.request', priority: 7 }

    # check security rules
    App\Middleware\SecurityCheckMiddleware:
        tags:
            - { name: 'kernel.event_listener', event: 'kernel.request', priority: 6 }

    # handle maintenance mode
    App\Middleware\MaintenanceMiddleware:
        tags:
            - { name: kernel.event_listener, event: kernel.request, priority: 5 }

    # auto-login middleware for remember me feature
    App\Middleware\AutoLoginMiddleware:
        tags:
            - { name: kernel.event_listener, event: kernel.request, priority: 4 }

    # check if user is banned
    App\Middleware\BannedCheckMiddleware:
        tags:
            - { name: kernel.event_listener, event: kernel.request, priority: 3 }

    # check if user is authenticated
    App\Middleware\AuthenticatedCheckMiddleware:
        tags:
            - { name: kernel.event_listener, event: kernel.request, priority: 2 }

    # check if user has permission to access component
    App\Middleware\AuthorizationMiddleware:
        tags:
            - { name: 'kernel.event_listener', event: 'kernel.request', priority: 1 }
