{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.4", "ext-ctype": "*", "ext-iconv": "*", "doctrine/doctrine-bundle": "^2.15", "doctrine/doctrine-migrations-bundle": "^3.4", "doctrine/orm": "^3.4", "minishlink/web-push": "^9.0", "phpoffice/phpspreadsheet": "^4.4", "symfony/console": "7.3.*", "symfony/dotenv": "7.3.*", "symfony/flex": "^2.7", "symfony/form": "7.3.*", "symfony/framework-bundle": "7.3.*", "symfony/mailer": "7.3.*", "symfony/maker-bundle": "^1.63", "symfony/monolog-bundle": "^3.10", "symfony/runtime": "7.3.*", "symfony/security-csrf": "7.3.*", "symfony/twig-bundle": "7.3.*", "symfony/validator": "7.3.*", "symfony/web-profiler-bundle": "7.3.*", "symfony/webpack-encore-bundle": "^2.2", "symfony/yaml": "7.3.*"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true}, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.3.*"}}, "require-dev": {"doctrine/doctrine-fixtures-bundle": "^4.1", "fakerphp/faker": "^1.24", "phpstan/phpstan": "^2.1", "phpstan/phpstan-doctrine": "^2.0", "phpstan/phpstan-phpunit": "^2.0", "phpstan/phpstan-symfony": "^2.0", "phpunit/phpunit": "^12.2", "robiningelbrecht/phpunit-pretty-print": "^1.4", "squizlabs/php_codesniffer": "^3.13", "symfony/browser-kit": "7.3.*", "symfony/css-selector": "7.3.*", "symfony/phpunit-bridge": "^7.3"}}