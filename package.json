{"name": "admin-suite", "version": "2.0", "description": "Solution for Linux server with services administration and monitoring", "main": "webpack.config.js", "author": "<PERSON><PERSON><PERSON><PERSON>", "license": "MIT", "private": true, "scripts": {"dev": "encore dev", "watch": "encore dev --watch", "build": "encore production --progress"}, "dependencies": {"svgo": "^3.0.2", "postcss": "^8.5.6", "apexcharts": "^3.54.1", "file-loader": "^6.2.0", "sortablejs": "^1.15.6", "tailwindcss": "^3.4.17", "autoprefixer": "^10.4.21", "highlight.js": "^11.11.1", "postcss-loader": "^8.1.1", "@fortawesome/fontawesome-free": "^6.7.2"}, "devDependencies": {"sass": "^1.89.2", "core-js": "^3.43.0", "webpack": "^5.74.0", "webpack-cli": "^5.1.4", "@babel/core": "^7.27.7", "sass-loader": "^16.0.5", "webpack-notifier": "^1.15.0", "@babel/preset-env": "^7.27.2", "regenerator-runtime": "^0.14.1", "@symfony/webpack-encore": "^5.1.0"}}