# deploy app to production server
name: Deploy

on:
  release:
    types: [created]

jobs:
  deploy:
    name: Deploy to production
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v2

    # deploy app with ssh action
    - name: Deploy with SSH
      uses: appleboy/ssh-action@master
      with:
        host: ${{ secrets.SSH_HOST }}
        username: ${{ secrets.SSH_USERNAME }}
        key: ${{ secrets.SSH_PRIVATE_KEY }}
        port: 22

        # run upgrade script on server 
        script: |
          # go to project directory
          cd /services/website/admin-suite

          # enable maintenance mode
          php bin/console app:toggle:maintenance true

          # stop admin-suite services
          sudo systemctl stop admin-suite-monitoring
          sudo systemctl stop apache2

          # clear cache & packages
          sudo sh scripts/clear.sh
                            
          # pull latest changes
          git pull

          # select production environment
          sed -i 's/^\(APP_ENV=\)dev/\1prod/' .env

          # install backend dependencies
          composer install --no-interaction --no-progress

          # install frontend dependencies
          npm install --loglevel=error

          # build frontend assets
          npm run build

          # run database migration
          php bin/console doctrine:database:create --if-not-exists
          php bin/console doctrine:migrations:migrate --no-interaction

          # start admin-suite services
          sudo systemctl start apache2
          sudo systemctl start admin-suite-monitoring

          # wait for ensure that services are started
          sleep 5

          # disable maintenance mode
          php bin/console app:toggle:maintenance false

          # set storage permissions
          sudo chmod -R 777 var/
          sudo chown -R www-data:www-data var/

          # send push notifications to users
          php bin/console app:notifications:send "[Actions]: new admin-suite release deployed!"
