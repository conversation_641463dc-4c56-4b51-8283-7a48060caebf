# static code analyse with phpstan
name: PHPStan

on:
  push:
    branches:
      - main
      - dev

jobs:
  phpstan:
    name: PHPStan analyse
    runs-on: ubuntu-latest

    steps:
    - name: Checkout repository
      uses: actions/checkout@v2

    # setup php interpreter
    - name: Setup PHP
      uses: shivammathur/setup-php@v2
      with:
        php-version: 8.4

    # install backend dependencies
    - name: Install backend dependencies
      run: composer install --no-interaction --no-progress

    # analyze code with phpstan
    - name: Run PHPStan
      run: ./bin/phpstan analyse --configuration phpstan.neon -vvv
