{% extends 'common/layout.twig' %}

{# METRICS-DASHBOARD COMPONENT #}
{% block component %}
<div class="flex flex-col h-full">
    {# SUB-NAVIGATION #}
    <div class="flex-shrink-0 px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
        <div class="flex items-center justify-between gap-2">
            <div class="flex items-center gap-3">
                <a href={{ path('app_dashboard') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to dashboard">
                    <i class="fas fa-arrow-left text-gray-300 text-xs"></i>
                </a>
                <div class="hidden sm:block">
                    <h1 class="text-xm font-bold text-white">Metrics Dashboard</h1>
                    <p class="text-gray-400 text-xs">System performance metrics</p>
                </div>
            </div>
            <div class="flex items-center gap-2">
                {# TIME PERIOD SELECTOR #}
                <div class="flex items-center gap-2">
                    <label for="time-period" class="text-xs text-gray-400 hidden sm:block">Period:</label>
                    <select id="time-period" onchange="updateTimePeriod()" class="h-8 px-3 bg-gray-800/95 text-white border border-gray-600/80 rounded text-xs font-medium focus:outline-none focus:ring-2 focus:ring-blue-500/90 focus:border-blue-500/90 hover:bg-gray-700/95 hover:border-gray-500/90 transition-all duration-200 cursor-pointer shadow-lg backdrop-blur-sm">
                        <option value="raw_metrics" class="bg-gray-800 text-white">{% if metricsSaveInterval == 60 %}Last hour{% else %}Last {{ metricsSaveInterval }} minutes{% endif %}</option>
                        <option value="last_24_hours" selected class="bg-gray-800 text-white">Last 24 hours</option>
                        <option value="last_week" class="bg-gray-800 text-white">Last week</option>
                        <option value="last_month" class="bg-gray-800 text-white">Last month</option>
                        <option value="all_time" class="bg-gray-800 text-white">All time</option>
                    </select>
                </div>

                {# ACTION BUTTONS #}
                <a href={{ path('app_manager_monitoring') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Go to monitoring">
                    <i class="fas fa-eye text-gray-300 text-xs"></i>
                </a>
                <button id="aggregateButton" class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Aggregate old metrics">
                    <i class="fas fa-compress text-gray-300 text-xs"></i>
                </button>
            </div>
        </div>
    </div>

    {# MAIN CONTENT AREA #}
    <div class="flex-1 flex flex-col min-h-0">
        <div class="metrics-component p-2 component">
            <div class="space-y-2">

                {# CURRENT USAGES CARD #}
                <div class="backdrop-blur-md bg-gray-800/50 border border-gray-700/50 rounded shadow-xl text-white w-full overflow-hidden transition-all duration-300">
                    <div class="px-2 py-2 border-b border-gray-700/50 text-gray-200 font-semibold tracking-wide text-sm uppercase">
                        <span>Current Usages</span>
                    </div>
                    <div class="p-1">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-1">
                            {# CPU USAGE #}
                            <div class="bg-gradient-to-br from-red-500/10 to-red-600/5 border border-red-500/20 rounded p-3 hover:from-red-500/15 hover:to-red-600/10 transition-all duration-300">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center gap-2">
                                        <div class="w-8 h-8 bg-red-500/20 rounded flex items-center justify-center">
                                            <i class="fas fa-microchip text-red-400 text-sm"></i>
                                        </div>
                                        <span class="text-gray-300 font-medium text-sm">CPU</span>
                                    </div>
                                    <span class="text-2xl font-bold text-red-400 cpu-percentage">{{ current_usages.cpu|e }}%</span>
                                </div>
                                <div class="w-full bg-gray-700/50 rounded-full h-2 overflow-hidden">
                                    <div class="h-full bg-gradient-to-r from-red-500 to-red-400 rounded-full transition-all duration-1500 ease-out cpu-bar" style="width: {{ current_usages.cpu|e }}%"></div>
                                </div>
                                <div class="mt-2 text-xs text-gray-400">Processor Usage</div>
                            </div>

                            {# RAM USAGE #}
                            <div class="bg-gradient-to-br from-blue-500/10 to-blue-600/5 border border-blue-500/20 rounded p-3 hover:from-blue-500/15 hover:to-blue-600/10 transition-all duration-300">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center gap-2">
                                        <div class="w-8 h-8 bg-blue-500/20 rounded flex items-center justify-center">
                                            <i class="fas fa-memory text-blue-400 text-sm"></i>
                                        </div>
                                        <span class="text-gray-300 font-medium text-sm">RAM</span>
                                    </div>
                                    <span class="text-2xl font-bold text-blue-400 ram-percentage">{{ current_usages.ram|e }}%</span>
                                </div>
                                <div class="w-full bg-gray-700/50 rounded-full h-2 overflow-hidden">
                                    <div class="h-full bg-gradient-to-r from-blue-500 to-blue-400 rounded-full transition-all duration-1500 ease-out ram-bar" style="width: {{ current_usages.ram|e }}%"></div>
                                </div>
                                <div class="mt-2 text-xs text-gray-400">Memory Usage</div>
                            </div>

                            {# STORAGE USAGE #}
                            <div class="bg-gradient-to-br from-green-500/10 to-green-600/5 border border-green-500/20 rounded p-3 hover:from-green-500/15 hover:to-green-600/10 transition-all duration-300">
                                <div class="flex items-center justify-between mb-3">
                                    <div class="flex items-center gap-2">
                                        <div class="w-8 h-8 bg-green-500/20 rounded flex items-center justify-center">
                                            <i class="fas fa-hdd text-green-400 text-sm"></i>
                                        </div>
                                        <span class="text-gray-300 font-medium text-sm">DISK</span>
                                    </div>
                                    <span class="text-2xl font-bold text-green-400 storage-percentage">{{ current_usages.storage|e }}%</span>
                                </div>
                                <div class="w-full bg-gray-700/50 rounded-full h-2 overflow-hidden">
                                    <div class="h-full bg-gradient-to-r from-green-500 to-green-400 rounded-full transition-all duration-1500 ease-out storage-bar" style="width: {{ current_usages.storage|e }}%"></div>
                                </div>
                                <div class="mt-2 text-xs text-gray-400">Storage Usage</div>
                            </div>
                        </div>
                    </div>
                </div>

                {# USAGE HISTORY CHARTS #}
                {% if data.metrics is empty and showRawMetrics %}
                    <div class="flex flex-col items-center justify-center py-12">
                        <div class="w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-chart-line text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-2">No raw metrics found in cache</h3>
                        <p class="text-gray-400 text-center">Raw metrics will appear here once the monitoring process collects some data.</p>
                    </div>
                {% elseif data.metrics is empty %}
                    <div class="flex flex-col items-center justify-center py-12">
                        <div class="w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-chart-line text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-2">No metrics found</h3>
                        <p class="text-gray-400 text-center">Metrics will appear here once the monitoring process collects and saves some data.</p>
                    </div>
                {% else %}
                    {% for metricName, metricData in data.metrics %}
                        <div class="backdrop-blur-md bg-gray-800/50 border border-gray-700/50 rounded shadow-xl text-white w-full overflow-hidden transition-all duration-300">
                            <div class="px-2 py-2 border-b border-gray-700/50 text-gray-200 font-semibold tracking-wide text-sm uppercase flex items-center justify-between">
                                <span>
                                    {{ metricName | replace({'_': ' '}) | capitalize }}
                                    {% if showRawMetrics %}
                                        <span class="text-xs text-gray-400 font-normal lowercase ml-1">(raw data)</span>
                                    {% else %}
                                        <span class="text-xs text-gray-400 font-normal lowercase ml-1">(history)</span>
                                    {% endif %}
                                </span>
                                {% if not showRawMetrics %}
                                    <button class="deleteButton inline-flex items-center gap-1 px-2 py-1 bg-red-500/10 hover:bg-red-500/20 text-red-400 hover:text-red-300 text-xs font-semibold rounded transition-all duration-200 border border-red-500/30" data-metric-name={{ metricName|e }} data-service-name="host-system" data-referer="app_metrics_dashboard">
                                        <span>Delete</span>
                                        <i class="fas fa-trash text-xs"></i>
                                    </button>
                                {% endif %}
                            </div>
                            <div class="chart-wrapper">
                                <div id="{{ (metricName ~ '-' ~ 'default')|replace({'.': '_', ' ': '_'}) }}-line" class="chart-container"></div>
                            </div>
                        </div>
                    {% endfor %}
                {% endif %}
            </div>
        </div>
    </div>
</div>

<script>
    // update time period in url
    function updateTimePeriod() {
        const select = document.getElementById('time-period')
        const selectedValue = select.value;
        window.location.search = `?time_period=${selectedValue}`
    }

    // select selecton value from url
    function setSelectedValue() {
        const urlParams = new URLSearchParams(window.location.search)
        const timePeriod = urlParams.get('time_period')
        const select = document.getElementById('time-period')

        // set selected value
        if (timePeriod) {
            select.value = timePeriod
        }
    }

    // init select value
    window.onload = setSelectedValue
</script>

{# SEND METRICS DATA TO CHARTS JS #}
<script>
    window.metricsData = {{ { categories: data.categories, metrics: data.metrics, serviceName: 'host-system', percentage: true } | json_encode | raw }}
</script>

{# DELETE CONFIRMATION POPUP #}
{% include "component/metrics/popup/delete-confirmation-popup.twig" %}
{{ encore_entry_script_tags('metrics-delete-confirmaton-js') }}

{# AGGREGATE CONFIRMATION POPUP #}
{% include "component/metrics/popup/aggregate-confirmation-popup.twig" %}
{{ encore_entry_script_tags('metrics-aggregate-confirmation-js') }}

{# INCLUDE CHARTS SCRIPT #}
{{ encore_entry_script_tags('metrics-charts-js') }}
{% endblock %}
