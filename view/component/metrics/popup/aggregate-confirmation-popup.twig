<div id="aggregatePopup" class="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center hidden z-50">
    <div class="bg-gray-800/95 border border-gray-700/50 rounded p-6 w-full max-w-lg mx-4 shadow-2xl">
        <div class="flex items-center gap-3 mb-4">
            <div class="w-8 h-8 bg-orange-500/20 rounded-full flex items-center justify-center">
                <i class="fas fa-compress text-orange-400"></i>
            </div>
            <h2 class="text-lg font-semibold text-white">Aggregate Old Metrics</h2>
        </div>
        <div class="mb-6">
            <p class="text-gray-300 mb-4">This will aggregate old metrics (older than 31 days) into monthly averages to save database space.</p>
            <div class="bg-gray-900/50 border border-gray-600/30 rounded p-4">
                <p class="text-white font-medium mb-3">What will happen:</p>
                <ul class="list-disc list-inside space-y-2 text-gray-300 text-sm">
                    <li>Old detailed metrics will be grouped by month</li>
                    <li>Monthly averages will be calculated and saved</li>
                    <li>Original detailed records will be removed</li>
                    <li>Recent metrics (last 31 days) will be preserved</li>
                </ul>
            </div>
        </div>
        <div class="flex justify-end gap-3">
            <button id="cancelAggregateButton" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded font-medium transition-colors duration-200">
                Cancel
            </button>
            <button id="confirmAggregateButton" class="px-4 py-2 bg-orange-600 hover:bg-orange-700 text-white rounded font-medium transition-colors duration-200">
                Aggregate Now
            </button>
        </div>
    </div>
</div>
