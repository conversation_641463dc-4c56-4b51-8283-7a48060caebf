{# CREATE NEW TODO FORM #}
{{ form_start(createTodoForm, {'attr': {'onsubmit': 'disableSubmitButton(this)'}}) }}
    <div class="bg-slate-800/80 flex-none backdrop-blur-sm">

        {# SUBMIT ERROR MESSAGE #}
        {% if createTodoForm.todo_text.vars.errors|length > 0 %}
            <div class="bg-red-500/20 text-red-300 text-sm border-t border-red-400/30 px-4 py-3">
                {{ form_errors(createTodoForm.todo_text) }}
            </div>
        {% endif %}

        <div class="flex-none">
            <div class="flex items-center">
                {# TODO TEXT INPUT #}
                {{ form_widget(createTodoForm.todo_text, {'attr': {
                    'class': 'flex-1 bg-slate-700/20 px-4 py-3 text-white outline-none border-none focus:bg-slate-700/20 focus:ring-1 focus:ring-slate-900/40 transition-all duration-200 placeholder-gray-400',
                    'placeholder': 'Enter new todo...'
                }})}}
                {# ADD TODO BUTTON #}
                <button type="submit" class="bg-green-600/80 hover:bg-green-500/90 text-white px-6 py-4 sm:py-3 focus:outline-none flex items-center add-todo-button transition-all duration-200 hover:shadow-md hover:shadow-green-500/20">
                    <i class="fa fa-plus text-lg" aria-hidden="true"></i>
                    <span class="hidden sm:inline ml-2 font-medium">Add Todo</span>
                </button>
            </div>
        </div>
    </div>
{{ form_end(createTodoForm) }}

{# DISABLE SUBMIT BUTTON WHEN FORM IS SUBMITTED (prevents multiple submits) #}
<script>
    function disableSubmitButton(form) {
        const submitButton = form.querySelector('.add-todo-button')
        if (submitButton) {
            submitButton.disabled = true
            submitButton.classList.add('opacity-50', 'cursor-not-allowed')
        }
    }
</script>
