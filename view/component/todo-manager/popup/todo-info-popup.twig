<div id="infoPopup" class="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center hidden z-50">
    <div class="backdrop-blur-md bg-gray-800/95 rounded p-6 border border-gray-700/50 w-full max-w-xl animate-popin">
        <div class="flex items-center mb-4">
            <i class="fas fa-info-circle text-blue-400 mr-3"></i>
            <h2 class="text-lg font-bold text-white">Task Information</h2>
        </div>
        <div class="space-y-3">
            <div class="flex items-center">
                <span class="text-gray-400 w-20">Owner:</span>
                <p id="todoOwner" class="text-white font-medium"></p>
            </div>
            <div class="flex items-center">
                <span class="text-gray-400 w-20">Status:</span>
                <p id="todoStatus" class="text-white font-medium"></p>
            </div>
            <div class="flex items-center">
                <span class="text-gray-400 w-20">Created:</span>
                <p id="todoCreatedAt" class="text-white font-medium"></p>
            </div>
            <div class="flex items-center">
                <span class="text-gray-400 w-20">Closed:</span>
                <p id="todoClosedAt" class="text-white font-medium"></p>
            </div>
        </div>
        <div class="flex justify-end mt-6">
            <button id="closePopup" class="bg-gray-600 hover:bg-gray-500 text-white px-4 py-2 rounded transition-colors duration-200">Close</button>
        </div>
    </div>
</div>
