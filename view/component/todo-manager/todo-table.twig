{% extends 'common/layout.twig' %}

{# TODO LIST COMPONENT #}
{% block component %}
<div class="flex flex-col h-full">
    {# SUB-NAVIGATION #}
    <div class="flex-shrink-0 px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
        <div class="flex items-center justify-between gap-2">
            <div class="flex items-center gap-3">
                <a href={{ path('app_dashboard') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to dashboard">
                    <i class="fas fa-arrow-left text-gray-300 text-xs"></i>
                </a>
                <div class="hidden sm:block">
                    <h1 class="text-xm font-bold text-white">Todo Manager</h1>
                    <p class="text-gray-400 text-xs">Manage your tasks and todos</p>
                </div>
            </div>
            <div class="flex items-center gap-2">
                {# STATUS FILTER BUTTONS #}
                {% if filter == 'closed' %}
                    <a href={{ path('app_todo_manager', {'filter': 'open'}) }} class="w-8 h-8 bg-gray-700/50 hover:bg-blue-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="View open todos">
                        <i class="fas fa-tasks text-gray-300 text-xs"></i>
                    </a>
                {% else %}
                    <a href={{ path('app_todo_manager', {'filter': 'closed'}) }} class="w-8 h-8 bg-gray-700/50 hover:bg-green-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="View closed todos">
                        <i class="fas fa-check text-gray-300 text-xs"></i>
                    </a>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="flex-grow overflow-y-auto">
        <div class="{{ filter == 'open' ? 'todo-list' : '' }} card-component p-0 px-2 pb-[2px] todo-manager overflow-hidden breadcrumb component">
            <div id="todo-items-container" class="divide-y divide-gray-700/50">

                {# TODO LIST IS EMPTY INFORMATION #}
                {% if todos|length == 0 %}
                    <p class="flex items-center justify-center mt-10 mb-20 text-2xl text-white font-bold">This todo list is empty</p>
                {% endif %}

                {# TODO ITEM #}
                {% for todo in todos %}
                    <div class="todo-item flex items-center justify-between p-3 my-2 rounded bg-gradient-to-r from-slate-800/60 to-slate-700/40 border border-slate-600/30 transition-all duration-200 hover:from-slate-700/70 hover:to-slate-600/50 hover:border-slate-500/40 hover:shadow-lg hover:shadow-slate-900/20 relative" data-todo-id={{ todo.id }} data-todo-text={{ todo.todoText|e('html_attr') }} data-position={{ todo.position }}>
                        {% if filter == 'open' %}
                            <div class="drag-handle mr-2 cursor-move text-gray-500 hover:text-white flex items-center justify-center">
                                <i class="fas fa-grip-vertical text-lg"></i>
                            </div>
                        {% endif %}
                        <div class="flex-grow">
                            <span class="text-white whitespace-pre-line disable-select-on-phone">{{ todo.todoText|linkify }}</span>
                        </div>
                        <div class="flex space-x-2 ml-1">
                            {% if filter == 'open' %}
                                {# EDIT BUTTON #}
                                <button data-todo-id={{ todo.id }} data-todo-text={{ todo.todoText|e('html_attr') }} class="edit-button text-blue-400 text-xl hover:text-blue-300 focus:outline-none">
                                    <i class="fa fa-edit" aria-hidden="true"></i>
                                </button>
                                {# COMPLETE BUTTON #}
                                <a href={{ path('app_todo_manager_close', {'id': todo.id}) }} class="delete-link text-green-400 text-xl hover:text-green-300 focus:outline-none mr-0.5">
                                    <i class="fa fa-check" aria-hidden="true"></i>
                                </a>
                            {% else %}
                                {# REOPEN BUTTON #}
                                <a href={{ path('app_todo_manager_reopen', {'id': todo.id}) }} class="delete-link text-green-400 text-xl hover:text-green-300 focus:outline-none mr-0.5">
                                    <i class="fa fa-undo" aria-hidden="true"></i>
                                </a>
                                {# DELETE BUTTON #}
                                <button data-delete-url={{ path('app_todo_manager_delete', {'id': todo.id}) }} class="delete-button delete-link text-red-400 text-xl hover:text-red-300 focus:outline-none mr-0.5">
                                    <i class="fa fa-trash" aria-hidden="true"></i>
                                </button>
                            {% endif %}
                            {# INFO BUTTON #}
                            <button class="info-button phone-none" data-todo-id={{ todo.id }} title="View Info">
                                <i class="fas fa-info-circle text-gray-400"></i>
                            </button>
                        </div>
                    </div>
                {% endfor %}
            </div>
        </div>
    </div>

    {# INCLUDE CREATE TODO FORM #}
    {% if filter == 'open' %}
        <div class="flex-shrink-0 sticky bottom-0 z-10">
            {% include "component/todo-manager/form/create-todo-form.twig" %}
        </div>
    {% endif %}
</div>

{# TODO INFO POPUP OVERLAY #}
{% include "component/todo-manager/popup/todo-info-popup.twig" %}

{# TODO EDIT POPUP OVERLAY #}
{% include "component/todo-manager/popup/todo-edit-popup.twig" %}

{# DELETE CONFIRMATION POPUP OVERLAY #}
{% include "component/todo-manager/popup/delete-confirmation-popup.twig" %}

{# INCLUDE FRONTEND FUNCTIONALITY #}
{{ encore_entry_script_tags('todo-manager-js') }}
{% endblock %}
