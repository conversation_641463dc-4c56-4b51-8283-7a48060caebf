{% extends 'common/layout.twig' %}

{# SYSTEM LOGS VIEW #}
{% block component %}
	{# SUB-NAVIGATION #}
	<div class="px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
		<div class="flex items-center gap-3">
			<a href={{ path('app_manager_logs') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to log manager">
				<i class="fas fa-arrow-left text-gray-300 text-xs"></i>
			</a>
			<div>
				<h1 class="text-xm font-bold text-white">System Logs</h1>
				<p class="text-gray-400 text-xs">Server log files</p>
			</div>
		</div>
	</div>

	{# LOG FILES LIST #}
	<div class="component">
		<div class="p-4">
			{% if logFiles|length < 1 %}
				<div class="flex flex-col items-center justify-center py-12">
					<div class="w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mb-4">
						<i class="fas fa-file-alt text-gray-400 text-2xl"></i>
					</div>
					<h3 class="text-xl font-bold text-white mb-2">No log files found</h3>
					<p class="text-gray-400 text-center">No system log files are available</p>
				</div>
			{% else %}
				<div class="space-y-3">
					{% for logFile in logFiles %}
						<div class="bg-gray-700/30 rounded p-4 border border-gray-600/30 hover:bg-gray-700/40 transition-all duration-200">
							<a href={{ path('app_file_system_view', { path: logFile.path, referer: 'log_manager_server_logs' } ) }} class="flex items-center justify-between group">
								<div class="flex items-center gap-3">
									<div class="w-8 h-8 bg-purple-500/20 rounded flex items-center justify-center">
										<i class="fas fa-file-alt text-purple-400 text-sm"></i>
									</div>
									<div>
										<div class="text-white font-medium text-sm group-hover:text-purple-300 transition-colors duration-200">{{ logFile.name }}</div>
										<div class="text-gray-400 text-xs">{{ logFile.path }}</div>
									</div>
								</div>
								<div class="text-gray-400 group-hover:text-purple-300 transition-colors duration-200">
									<i class="fas fa-chevron-right text-xs"></i>
								</div>
							</a>
						</div>
					{% endfor %}
				</div>
			{% endif %}
		</div>
	</div>
{% endblock %}
