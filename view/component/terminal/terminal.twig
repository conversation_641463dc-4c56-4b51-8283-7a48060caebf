{% extends 'common/layout.twig' %}

{# TERMINAL COMPONENT VIEW #}
{% block component %}
<div class="flex flex-col h-full">
    {# SUB-NAVIGATION #}
    <div class="flex-shrink-0 px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
        <div class="flex items-center justify-between gap-2">
            <div class="flex items-center gap-3">
                <a href={{ path('app_dashboard') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to dashboard">
                    <i class="fas fa-arrow-left text-gray-300 text-xs"></i>
                </a>
                <div class="hidden sm:block">
                    <h1 class="text-xm font-bold text-white">Terminal</h1>
                    <p class="text-gray-400 text-xs">Interactive command line interface</p>
                </div>
            </div>
        </div>
    </div>

    {# TERMINAL COMPONENT #}
    <div class="component" style="background: rgba(12, 17, 28, 255);">
        <div class="terminal-component">
            <div id="terminal">
                <pre id="output-container" class="whitespace-pre-wrap break-words"></pre>
                <div id="command-container">
                    <div id="prompt-line">
                        <span class="text-green-500" id="usermame">...</span><span class="text-white">:</span><span id="path" class="text-blue-600">...</span><span id="prompt" class="text-white">$</span>
                    </div>
                    <input type="text" id="command" autocorrect="off" autocapitalize="none" autocomplete="off" spellcheck="false" inputmode="text" pattern=".*">
                </div>
            </div>
        </div>
    </div>
    
    {# INCLUDE TERMINAL JS FUNCTIONALITY #}
    {{ encore_entry_script_tags('terminal-component-js') }}
</div>
{% endblock %}
