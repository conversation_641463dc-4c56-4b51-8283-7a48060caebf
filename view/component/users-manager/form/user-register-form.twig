{% extends 'common/layout.twig' %}

{# ADMIN ACCOUNTS REGISTER COMPONENT (ADMIN USER MANAGER) #}
{% block component %}
<div class="flex flex-col h-full">
    {# SUB-NAVIGATION #}
    <div class="flex-shrink-0 px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
        <div class="flex items-center justify-between gap-2">
            <div class="flex items-center gap-3">
                <a href={{ path('app_manager_users') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to users manager">
                    <i class="fas fa-arrow-left text-gray-300 text-xs"></i>
                </a>
                <div class="hidden sm:block">
                    <h1 class="text-xm font-bold text-white">Register User</h1>
                    <p class="text-gray-400 text-xs">Create a new system user account</p>
                </div>
            </div>
        </div>
    </div>

    {# MAIN CONTENT AREA #}
    <div class="flex-1 flex flex-col min-h-0 sm:mt-10">
        <div class="component p-2">
            {# REGISTER FORM #}
            <div class="max-w-2xl mx-auto">
                <div class="bg-gray-800/50 border border-gray-700/50 rounded overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-700/50 bg-gray-800/30">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-blue-500/20 rounded flex items-center justify-center">
                                <i class="fas fa-user-plus text-blue-400 text-lg"></i>
                            </div>
                            <div>
                                <h2 class="text-xl font-bold text-white">Register New User</h2>
                                <p class="text-gray-400 text-sm">Create a new system user account</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6">
                        {# FLASH MESSAGES #}
                        {% for message in app.flashes('success') %}
                            <div class="mb-4 p-3 rounded bg-green-500/20 text-green-300 border border-green-500/30">
                                <i class="fas fa-check-circle mr-2"></i>{{ message|e }}
                            </div>
                        {% endfor %}

                        {% for message in app.flashes('error') %}
                            <div class="mb-4 p-3 rounded bg-red-500/20 text-red-300 border border-red-500/30">
                                <i class="fas fa-exclamation-circle mr-2"></i>{{ message|e }}
                            </div>
                        {% endfor %}

                        {# REGISTER FORM #}
                        {{ form_start(registrationForm) }}

                            {# FORM FIELDS #}
                            <div class="space-y-4 mb-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Username</label>
                                    {{ form_row(registrationForm.username, {'attr': {'class': 'w-full text-white px-4 py-3 border border-gray-600/50 rounded focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 outline-none transition-all duration-200 user-manager-input', 'placeholder': 'Enter username'}}) }}
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Password</label>
                                    {{ form_row(registrationForm.password.first, {'attr': {'class': 'w-full text-white px-4 py-3 border border-gray-600/50 rounded focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 outline-none transition-all duration-200 user-manager-input', 'placeholder': 'Enter password'}}) }}
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-300 mb-2">Confirm Password</label>
                                    {{ form_row(registrationForm.password.second, {'attr': {'class': 'w-full text-white px-4 py-3 border border-gray-600/50 rounded focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 outline-none transition-all duration-200 user-manager-input', 'placeholder': 'Confirm password'}}) }}
                                </div>
                            </div>

                            {# FORM SUBMIT BUTTONS #}
                            <div class="flex items-center justify-between gap-4">
                                <a href={{ path('app_manager_users') }} class="inline-flex items-center gap-2 px-4 py-2 bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white rounded border border-gray-600/30 transition-all duration-200 text-sm font-medium">
                                    <i class="fas fa-arrow-left text-xs"></i>
                                    <span>Cancel</span>
                                </a>
                                <button type="submit" class="inline-flex items-center gap-2 px-6 py-2 bg-blue-500/20 hover:bg-blue-500/30 text-blue-300 hover:text-blue-200 rounded border border-blue-500/30 transition-all duration-200 text-sm font-medium">
                                    <i class="fas fa-user-plus text-xs"></i>
                                    <span>Register User</span>
                                </button>
                            </div>
                        {{ form_end(registrationForm) }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
