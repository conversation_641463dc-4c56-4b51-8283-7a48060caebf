{% extends 'common/layout.twig' %}

{# DIAGNOSTICS PAGE #}
{% block component %}
<div class="flex flex-col h-full">
{# SUB-NAVIGATION #}
<div class="flex-shrink-0 px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
    <div class="flex items-center justify-between gap-2">
        <div class="flex items-center gap-3">
            <a href={{ path('app_dashboard') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to dashboard">
                <i class="fas fa-arrow-left text-gray-300 text-xs"></i>
            </a>
            <div class="hidden sm:block">
                <h1 class="text-xm font-bold text-white">System Diagnostics</h1>
                <p class="text-gray-400 text-xs">System health and status checks</p>
            </div>
        </div>

        <div class="flex items-center gap-1">
            <a href={{ path('app_system_audit') }} class="w-8 h-8 bg-blue-500/20 hover:bg-blue-500/30 rounded flex items-center justify-center transition-all duration-200 border border-blue-500/30" title="System audit">
                <i class="fas fa-columns text-blue-400 text-xs"></i>
            </a>
        </div>
    </div>
</div>

{# MAIN CONTENT AREA #}
<div class="flex-1 flex flex-col min-h-0">
    <div class="diagnostics-component p-2 component">
        <div class="space-y-2">
            {# HOST SYSTEM DIAGNOSTICS CARD #}
            {% include "component/diagnostic/card/system-diagnostics.twig" %}

            {# SELF SUITE DIAGNOSTICS CARD #}
            {% include "component/diagnostic/card/self-suite-diagnostics.twig" %}

            {# SERVICES DIAGNOSTICS CARD #}
            {% include "component/diagnostic/card/services-diagnostics.twig" %}
        </div>
    </div>
</div>
{% endblock %}
