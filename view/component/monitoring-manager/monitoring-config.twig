{% extends 'common/layout.twig' %}

{# SERVICES CONFIG OVERVIEW #}
{% block component %}
<div class="flex flex-col h-full">
    {# SUB-NAVIGATION #}
    <div class="flex-shrink-0 px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
        <div class="flex items-center justify-between gap-2">
            <div class="flex items-center gap-3">
                <a href={{ path('app_manager_monitoring') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to monitoring dashboard">
                    <i class="fas fa-arrow-left text-gray-300 text-xs"></i>
                </a>
                <div class="hidden sm:block">
                    <h1 class="text-xm font-bold text-white">Services Configuration</h1>
                    <p class="text-gray-400 text-xs">Service monitoring settings</p>
                </div>
            </div>
        </div>
    </div>

    {# MAIN CONTENT AREA #}
    <div class="flex-1 flex flex-col min-h-0">
        <div class="monitoring-config md:p-4 component">
            <div class="max-w-7xl mx-auto space-y-4">
                {% for service in services %}
                    {% if service.type == 'systemd' %}
                        <div class="bg-gray-800/50 border border-gray-700/50 sm:rounded">
                            <div class="bg-gray-700/50 px-4 py-3 border-b border-gray-600/50">
                                <div class="flex items-center gap-3">
                                    <div class="w-8 h-8 bg-blue-500/20 rounded-full flex items-center justify-center">
                                        <i class="fas fa-cogs text-blue-400"></i>
                                    </div>
                                    <h2 class="text-lg font-semibold text-gray-200">{{ service.display_name|e }}</h2>
                                </div>
                            </div>
                            <div class="p-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="space-y-3">
                                        <div>
                                            <span class="text-gray-400 text-sm">Service Name:</span>
                                            <p class="text-white font-medium">{{ service.service_name|e }}</p>
                                        </div>
                                        <div>
                                            <span class="text-gray-400 text-sm">Display Name:</span>
                                            <p class="text-white font-medium">{{ service.display_name|e }}</p>
                                        </div>
                                    </div>
                                    <div class="space-y-3">
                                        <div>
                                            <span class="text-gray-400 text-sm">Type:</span>
                                            <p class="text-white font-medium">{{ service.type|e }}</p>
                                        </div>
                                        <div>
                                            <span class="text-gray-400 text-sm">Display:</span>
                                            <p class="text-white font-medium">{{ service.display|e ? 'Yes' : 'No' }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% elseif service.type == 'http' %}
                        <div class="bg-gray-800/50 border border-gray-700/50 sm:rounded">
                            <div class="bg-gray-700/50 px-4 py-3 border-b border-gray-600/50">
                                <div class="flex items-center gap-3">
                                    <div class="w-8 h-8 bg-purple-500/20 rounded-full flex items-center justify-center">
                                        <i class="fas fa-globe text-purple-400"></i>
                                    </div>
                                    <h2 class="text-lg font-semibold text-gray-200">{{ service.display_name|e }}</h2>
                                </div>
                            </div>
                            <div class="p-4">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div class="space-y-3">
                                        <div>
                                            <span class="text-gray-400 text-sm">Service Name:</span>
                                            <p class="text-white font-medium">{{ service.service_name|e }}</p>
                                        </div>
                                        <div>
                                            <span class="text-gray-400 text-sm">Display Name:</span>
                                            <p class="text-white font-medium">{{ service.display_name|e }}</p>
                                        </div>
                                        <div>
                                            <span class="text-gray-400 text-sm">URL:</span>
                                            <p class="text-white font-medium">
                                                <a href={{ service.url|e }} target="_blank" class="text-blue-400 hover:text-blue-300 transition-colors duration-200">{{ service.url|e }}</a>
                                            </p>
                                        </div>
                                        <div>
                                            <span class="text-gray-400 text-sm">Accept Codes:</span>
                                            <p class="text-white font-medium">{{ service.accept_codes|join(', ') }}</p>
                                        </div>
                                    </div>
                                    <div class="space-y-3">
                                        <div>
                                            <span class="text-gray-400 text-sm">Max Response Time:</span>
                                            <p class="text-white font-medium">{{ service.max_response_time|e }}ms</p>
                                        </div>
                                        <div>
                                            <span class="text-gray-400 text-sm">Type:</span>
                                            <p class="text-white font-medium">{{ service.type|e }}</p>
                                        </div>
                                        <div>
                                            <span class="text-gray-400 text-sm">Display:</span>
                                            <p class="text-white font-medium">{{ service.display|e ? 'Yes' : 'No' }}</p>
                                        </div>
                                        <div>
                                            <span class="text-gray-400 text-sm">Metrics Enabled:</span>
                                            <p class="text-white font-medium">{{ service.metrics_monitoring.collect_metrics|e ? 'Yes' : 'No' }}</p>
                                        </div>
                                        {% if service.metrics_monitoring.collect_metrics == true %}
                                            <div>
                                                <span class="text-gray-400 text-sm">Metrics Exporter:</span>
                                                <p class="text-white font-medium">
                                                    <a href={{ service.metrics_monitoring.metrics_collector_url|e }} target="_blank" class="text-blue-400 hover:text-blue-300 transition-colors duration-200">
                                                        {{ service.metrics_monitoring.metrics_collector_url|e }}
                                                    </a>
                                                </p>
                                            </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>
                    {% endif %}
                {% endfor %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
