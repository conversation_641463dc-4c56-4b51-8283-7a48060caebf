<div class="backdrop-blur-md bg-gray-800/50 border border-gray-700/50 rounded shadow-xl text-white w-full overflow-hidden transition-all duration-300 h-full flex flex-col">
    <div class="px-2 py-2 border-b border-gray-700/50 font-semibold tracking-wide text-sm uppercase text-gray-200 flex items-center gap-2">
        <div class="w-8 h-8 bg-green-500/20 rounded flex items-center justify-center">
            <i class="fas fa-database text-green-400 text-sm"></i>
        </div>
        <span>Database Statistics</span>
    </div>
    <div class="p-[5.5px] flex-1 flex flex-col justify-between space-y-2">
        {# SECTION: LOGS #}
        <div class="bg-gradient-to-r from-gray-700/10 to-gray-600/10 rounded p-3 border border-gray-600/30 backdrop-blur-sm">
            <div class="flex items-center justify-between mb-2">
                <div class="flex items-center gap-2">
                    <i class="fas fa-scroll text-orange-400 text-sm"></i>
                    <span class="text-white font-medium text-sm">Logs</span>
                </div>
                <a href={{ path('app_manager_logs') }} class="inline-flex items-center gap-1 px-2 py-1 bg-blue-500/10 hover:bg-blue-500/20 text-blue-400 hover:text-blue-300 text-xs font-semibold rounded transition-all duration-200 border border-blue-500/30" title="Go to logs">
                    <span>View All</span>
                    <i class="fas fa-external-link-alt text-xs"></i>
                </a>
            </div>
            <div class="grid grid-cols-2 gap-2 text-xs text-gray-400">
                <div>Total Logs: <span class="text-white font-semibold">{{ allLogsCount|e }}</span></div>
                <div>Auth Logs: <span class="text-white font-semibold">{{ authLogsCount|e }}</span></div>
                <div>Read: <span class="text-green-400 font-semibold">{{ readedLogsCount|e }}</span></div>
                <div>Unread: <span class="text-red-400 font-semibold">{{ unreadedLogsCount|e }}</span></div>
            </div>
        </div>

        {# SECTION: USERS #}
        <div class="bg-gradient-to-r from-gray-700/10 to-gray-600/10 rounded p-3 border border-gray-600/30 backdrop-blur-sm">
            <div class="flex items-center justify-between mb-2">
                <div class="flex items-center gap-2">
                    <i class="fas fa-users text-purple-400 text-sm"></i>
                    <span class="text-white font-medium text-sm">Users</span>
                </div>
                <a href={{ path('app_manager_users') }} class="inline-flex items-center gap-1 px-2 py-1 bg-blue-500/10 hover:bg-blue-500/20 text-blue-400 hover:text-blue-300 text-xs font-semibold rounded transition-all duration-200 border border-blue-500/30" title="Go to users manager">
                    <span>View All</span>
                    <i class="fas fa-external-link-alt text-xs"></i>
                </a>
            </div>
            <div class="grid grid-cols-2 gap-2 text-xs text-gray-400">
                <div>Total Users: <span class="text-white font-semibold">{{ usersCount|e }}</span></div>
                <div>Online: <span class="text-green-400 font-semibold">{{ onlineUsersCount|e }}</span> <a href="{{ path('app_manager_users', {'filter': 'online'}) }}" class="text-blue-400 hover:text-blue-300">(list)</a></div>
                <div class="col-span-2">Banned: <span class="text-red-400 font-semibold">{{ bannedUsersCount|e }}</span> <a href="{{ path('app_manager_users', {'filter': 'banned'}) }}" class="text-blue-400 hover:text-blue-300">(list)</a></div>
            </div>
        </div>
    </div>
</div>
