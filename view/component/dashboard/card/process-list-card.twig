<div class="backdrop-blur-md bg-gray-800/50 border border-gray-700/50 rounded shadow-xl text-white w-full overflow-hidden transition-all duration-300">
    <div class="px-2 py-2 border-b border-gray-700/50 font-semibold tracking-wide text-sm uppercase text-gray-200 flex items-center gap-2">
        <div class="w-8 h-8 bg-yellow-500/20 rounded flex items-center justify-center">
            <i class="fas fa-tasks text-yellow-400 text-sm"></i>
        </div>
        <span>Process List</span>
    </div>
    <div class="overflow-auto max-h-[296px] custom-scrollbar"> 
        <table class="min-w-full text-white text-sm">
            <thead class="bg-gray-700/50 backdrop-blur-sm">
                <tr class="border-b border-gray-600/50">
                    <th scope="col" class="px-3 py-2 text-left text-xs font-semibold uppercase tracking-wider sticky top-0 bg-gray-800 backdrop-blur-md z-10 text-gray-200">
                        <div class="flex items-center">
                            <i class="fas fa-hashtag text-gray-400 mr-2"></i>
                            PID
                        </div>
                    </th>
                    <th scope="col" class="px-3 py-2 text-left text-xs font-semibold uppercase tracking-wider sticky top-0 bg-gray-800 backdrop-blur-md z-10 text-gray-200">
                        <div class="flex items-center">
                            <i class="fas fa-user text-gray-400 mr-2"></i>
                            User
                        </div>
                    </th>
                    <th scope="col" class="px-3 py-2 text-left text-xs font-semibold uppercase tracking-wider sticky top-0 bg-gray-800 backdrop-blur-md z-10 text-gray-200">
                        <div class="flex items-center">
                            <i class="fas fa-cog text-gray-400 mr-2"></i>
                            Process
                        </div>
                    </th>
                </tr>
            </thead>
            <tbody class="divide-y divide-gray-600/30">
                {% for process in processList %}
                    <tr class="hover:bg-gradient-to-r hover:from-gray-600/20 hover:to-gray-500/20 transition-all duration-200 group">
                        <td class="px-3 py-2 whitespace-nowrap font-mono text-xs text-blue-300 group-hover:text-blue-200">{{ process.pid|e }}</td>
                        <td class="px-3 py-2 whitespace-nowrap text-gray-300 group-hover:text-gray-200 font-medium">{{ process.user|e }}</td>
                        <td class="px-3 py-2 whitespace-nowrap text-gray-400 group-hover:text-gray-300 truncate max-w-[200px]" title="{{ process.process|e }}">{{ process.process|e }}</td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
    </div>
</div>
