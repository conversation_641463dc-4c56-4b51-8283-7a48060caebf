{% extends 'common/layout.twig' %}

{# MAIN ADMIN-SUITE DASHBOARD #}
{% block component %}
<div class="w-full dashbard-component bg-gray-900 min-h-screen">
    {# MAIN DASHBOARD CARDS #}
    <div class="grid grid-cols-1 sm:grid-cols-2 gap-2 p-2 component">
        {# WARNINGS BOX CARD #}
        {% include "component/dashboard/card/warning-card.twig" %}

        {# SERVICES DASHBOARD CARD #}
        {% include "component/dashboard/card/services-card.twig" %}

        {# PROCESS LIST DASHBOARD CARD #}
        {% include "component/dashboard/card/process-list-card.twig" %}

        {# NETWORK USAGE #}
        {% include "component/dashboard/card/network-usage-card.twig" %}

        {# HOST SYSTEM RESOURCES #}
        {% include "component/dashboard/card/system-resources-card.twig" %}

        {# HOST SYSTEM INFO #}
        {% include "component/dashboard/card/system-info-card.twig" %}

        {# DATABASE STATISTICS #}
        {% include "component/dashboard/card/database-statisctis-card.twig" %}
    </div>
</div>

{# HIDE WARNING BOX IF NO WARNINGS FOUND #}
<script>
    window.addEventListener('DOMContentLoaded', (event) => {
        const divElement = document.getElementById('warning-box')
        const elements = document.getElementById('wraning-elements')
        if (elements.innerHTML.trim() === '') {
            divElement.style.display = 'none'
        } else {
            divElement.style.display = 'block'
        }
    })
</script>

{# SYSTEM RESOURCES UPDATER #}
{{ encore_entry_script_tags('system-resources-updater-js') }}
{% endblock %}
