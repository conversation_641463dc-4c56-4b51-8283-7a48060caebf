{% extends 'common/layout.twig' %}

{# FILE SYSTEM BROWSER COMPONENT #}
{% block component %}
<div class="flex flex-col h-full">
    {# SUB-NAVIGATION #}
    <div class="flex-shrink-0 px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
        {# GET BACK PATH #}
        {% set pathParts = currentPath|split('/') %}
        {% if pathParts|length > 1 %}
            {% set pathParts = pathParts|filter(p => p != '') %}
            {% if pathParts|length > 1 %}
                {% set backPath = pathParts|slice(0, -1)|join('/') %}
                {% set backPath = '/' ~ backPath %}
            {% else %}
                {% set backPath = '/' %}
            {% endif %}
        {% else %}
            {% set backPath = '/' %}
        {% endif %}

        <div class="flex items-center justify-between gap-2">
            <div class="flex items-center gap-3">
                {# BACK BUTTON #}
                {% if backPath == '/' and currentPath == '/' %}
                    <a href={{ path('app_dashboard') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to dashboard">
                        <i class="fas fa-arrow-left text-gray-300 text-xs"></i>
                    </a>
                {% else %}
                    <a href={{ path('app_file_system_browser', { 'path': backPath }) }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to previous directory">
                        <i class="fas fa-arrow-left text-gray-300 text-xs"></i>
                    </a>
                {% endif %}

                <div class="hidden sm:block">
                    <h1 class="text-xm font-bold text-white">Filesystem Browser</h1>
                    <p class="text-gray-400 text-xs">Browse and manage system files</p>
                </div>
            </div>

            <div class="flex items-center gap-2">
                {# CREATE MENU BUTTON #}
                <div class="relative inline-block">
                    <button id="create-menu-button" class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Create new item">
                        <i class="fas fa-plus text-gray-300 text-xs"></i>
                    </button>
                    <div id="create-menu" class="absolute right-0 mt-1 w-56 rounded shadow-lg bg-gray-800/95 backdrop-blur-sm border border-gray-700/50 z-10 hidden overflow-hidden">
                        <div class="py-1">
                            <a href={{ path('app_file_system_create', { 'path': currentPath == '' ? '/' : currentPath }) }} class="flex items-center px-4 py-2 text-sm text-white hover:bg-gray-700/50 transition-colors duration-200">
                                <i class="fas fa-file text-blue-400 mr-3 w-4 text-center"></i>
                                <span class="font-medium">Create New File</span>
                            </a>
                            <a href={{ path('app_file_system_create_directory', { 'path': currentPath == '' ? '/' : currentPath }) }} class="flex items-center px-4 py-2 text-sm text-white hover:bg-gray-700/50 transition-colors duration-200">
                                <i class="fas fa-folder text-yellow-400 mr-3 w-4 text-center"></i>
                                <span class="font-medium">Create New Folder</span>
                            </a>
                        </div>
                    </div>
                </div>

                {# UPLOAD BUTTON #}
                <a href={{ path('app_file_system_upload', { 'path': currentPath == '' ? '/' : currentPath }) }} class="w-8 h-8 bg-gray-700/50 hover:bg-green-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Upload files">
                    <i class="fas fa-upload text-gray-300 text-xs"></i>
                </a>

                {# TERMINAL BUTTON #}
                <a href={{ path('app_terminal') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Open terminal">
                    <i class="fas fa-terminal text-gray-300 text-xs"></i>
                </a>
            </div>
        </div>
    </div>

    {# MAIN CONTENT AREA #}
    <div class="flex-1 flex flex-col min-h-0">
        <div class="component">
            {# FILESYSTEM PATH BREADCRUMB #}
            <div class="px-1 py-2 border-b border-gray-700/30 bg-gray-800/30 overflow-x-auto">
                <div class="flex items-center space-x-1 px-1 whitespace-nowrap">
                    <i class="fas fa-folder-open text-blue-400 mr-1"></i>
                    <div class="text-sm text-gray-100 flex items-center">
                        {% set parts = currentPath|split('/') %}
                        <a href={{ path('app_file_system_browser', { 'path': '/' }) }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">/base</a>
                        {% set accumulatedPath = '' %}
                        {% for part in parts %}
                            {% if part is not empty %}
                                {% set accumulatedPath = accumulatedPath ~ '/' ~ part %}
                                <span class="text-gray-500 mx-0">/</span>
                                <a href={{ path('app_file_system_browser', { 'path': accumulatedPath }) }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">{{ part }}</a>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>

            {# SET CURRENT PATH EMPTY IF IT IS THE ROOT PATH #}
            {% if currentPath == '/' %}
                {% set currentPath = '' %}
            {% endif %}

            {# FLASH MESSAGES #}
            {% for label, messages in app.flashes %}
                {% for message in messages %}
                    <div class="p-2 {% if label == 'success' %}bg-green-600/20 text-green-400 {% else %}bg-red-600/20 text-red-400 border border-red-800{% endif %}">
                        {{ message|e }}
                    </div>
                {% endfor %}
            {% endfor %}

            {# FILES LIST TABLE #}
            <div class="overflow-auto">
                {% if filesystemList|length == 0 %}
                    <div class="flex flex-col items-center justify-center py-12">
                        <div class="w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mb-4">
                            <i class="fas fa-folder-open text-gray-400 text-2xl"></i>
                        </div>
                        <h3 class="text-xl font-bold text-white mb-2">No files found</h3>
                        <p class="text-gray-400 text-center">The directory is empty</p>
                    </div>
                {% else %}
                    <table class="min-w-full text-white text-sm border-b border-gray-700/50">
                        <thead class="bg-gray-700/50">
                            <tr class="border-b border-gray-700/60">
                                <th scope="col" class="px-2 py-2 text-left text-xs font-semibold uppercase tracking-wider text-gray-200">
                                    <div class="flex items-center p-1">
                                        <i class="fas fa-file text-gray-400 mr-2"></i>
                                        Name
                                    </div>
                                </th>
                                <th scope="col" class="px-2 py-2 text-left text-xs font-semibold uppercase tracking-wider text-gray-200">
                                    <div class="flex items-center p-1">
                                        <i class="fas fa-weight text-gray-400 mr-2"></i>
                                        Size
                                    </div>
                                </th>
                                <th scope="col" class="px-2 py-2 text-left text-xs font-semibold uppercase tracking-wider text-gray-200">
                                    <div class="flex items-center p-1">
                                        <i class="fas fa-lock text-gray-400 mr-2"></i>
                                        Permissions
                                    </div>
                                </th>
                                <th scope="col" class="px-2 py-2 text-left text-xs font-semibold uppercase tracking-wider text-gray-200">
                                    <div class="flex items-center p-1">
                                        <i class="fas fa-clock text-gray-400 mr-2"></i>
                                        Modified
                                    </div>
                                </th>
                            </tr>
                        </thead>
                        <tbody class="divide-y divide-gray-600/30">
                            {% for file in filesystemList %}
                                <tr class="bg-gray-800/50 hover:bg-gradient-to-r hover:from-gray-600/20 hover:to-gray-500/20 transition-all duration-200 group">
                                    <td class="px-2 py-2 text-left">
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center">
                                                {% if file.isDir %}
                                                    <i class="fas fa-folder text-yellow-400 mr-3"></i>
                                                    <a href={{ path('app_file_system_browser', { 'path': currentPath ~ '/' ~ file.name }) }} class="text-blue-400 hover:text-blue-300 font-medium transition-colors duration-200">
                                                        {{ file.name|e }}
                                                    </a>
                                                {% else %}
                                                    <i class="fas fa-file text-gray-400 mr-3"></i>
                                                    <a href={{ path('app_file_system_view', { 'path': currentPath ~ '/' ~ file.name }) }} class="text-white hover:text-blue-300 transition-colors duration-200">
                                                        {{ file.name|e }}
                                                    </a>
                                                {% endif %}
                                            </div>
                                            {# DESKTOP ACTIONS - Hidden on mobile #}
                                            <div class="hidden sm:flex items-center gap-1">
                                                {% if not file.isDir %}
                                                    <a href={{ path('app_file_system_download', { 'path': currentPath ~ '/' ~ file.name }) }} class="w-7 h-7 bg-green-500/20 hover:bg-green-500/30 border border-green-500/30 rounded flex items-center justify-center transition-all duration-200" id="loading-blocker" title="Download file">
                                                        <i class="fas fa-download text-xs text-green-400"></i>
                                                    </a>
                                                {% endif %}
                                                {% if not file.isDir and file.isEditable %}
                                                    <a href={{ path('app_file_system_edit', { 'path': currentPath ~ '/' ~ file.name }) }} class="w-7 h-7 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 rounded flex items-center justify-center transition-all duration-200" title="Edit file">
                                                        <i class="fas fa-edit text-xs text-blue-400"></i>
                                                    </a>
                                                {% endif %}
                                                <a href={{ path('app_file_system_rename', { 'path': currentPath ~ '/' ~ file.name }) }} class="w-7 h-7 bg-yellow-500/20 hover:bg-yellow-500/30 border border-yellow-500/30 rounded flex items-center justify-center transition-all duration-200" title="Rename {{ file.isDir ? 'directory' : 'file' }}">
                                                    <i class="fas fa-pen text-xs text-yellow-400"></i>
                                                </a>
                                                <a href={{ path('app_file_system_move', { 'path': currentPath ~ '/' ~ file.name }) }} class="w-7 h-7 bg-purple-500/20 hover:bg-purple-500/30 border border-purple-500/30 rounded flex items-center justify-center transition-all duration-200" title="Move {{ file.isDir ? 'directory' : 'file' }}">
                                                    <i class="fas fa-arrows-alt text-xs text-purple-400"></i>
                                                </a>
                                                <button class="delete-file-button w-7 h-7 bg-red-500/20 hover:bg-red-500/30 border border-red-500/30 rounded flex items-center justify-center transition-all duration-200" data-path="{{ currentPath ~ '/' ~ file.name }}" data-is-dir="{{ file.isDir ? 'true' : 'false' }}" title="Delete {{ file.isDir ? 'directory' : 'file' }}">
                                                    <i class="fas fa-trash text-xs text-red-400"></i>
                                                </button>
                                            </div>

                                            {# MOBILE ACTIONS - Dropdown menu #}
                                            <div class="sm:hidden relative" data-dropdown>
                                                <button class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 border border-gray-600/30 rounded flex items-center justify-center transition-all duration-200 z-1" onclick="toggleDropdown(this)" title="Actions">
                                                    <i class="fas fa-ellipsis-v text-xs text-gray-300"></i>
                                                </button>

                                                {# DROPDOWN MENU #}
                                                <div class="w-48 bg-gray-800 border border-gray-600/30 rounded-lg shadow-xl hidden z-10" data-dropdown-menu>
                                                    {% if not file.isDir %}
                                                        <a href={{ path('app_file_system_download', { 'path': currentPath ~ '/' ~ file.name }) }} class="flex items-center gap-3 px-2 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700/50 transition-all duration-200 border-b border-gray-700/30" id="loading-blocker">
                                                            <i class="fas fa-download text-green-400 w-4"></i>
                                                            <span>Download</span>
                                                        </a>
                                                    {% endif %}
                                                    {% if not file.isDir and file.isEditable %}
                                                        <a href={{ path('app_file_system_edit', { 'path': currentPath ~ '/' ~ file.name }) }} class="flex items-center gap-3 px-2 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700/50 transition-all duration-200 border-b border-gray-700/30">
                                                            <i class="fas fa-edit text-blue-400 w-4"></i>
                                                            <span>Edit</span>
                                                        </a>
                                                    {% endif %}
                                                    <a href={{ path('app_file_system_rename', { 'path': currentPath ~ '/' ~ file.name }) }}
                                                    class="flex items-center gap-3 px-2 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700/50 transition-all duration-200 border-b border-gray-700/30">
                                                        <i class="fas fa-pen text-yellow-400 w-4"></i>
                                                        <span>Rename</span>
                                                    </a>
                                                    <a href={{ path('app_file_system_move', { 'path': currentPath ~ '/' ~ file.name }) }} class="flex items-center gap-3 px-2 py-2 text-sm text-gray-300 hover:text-white hover:bg-gray-700/50 transition-all duration-200 border-b border-gray-700/30">
                                                        <i class="fas fa-arrows-alt text-purple-400 w-4"></i>
                                                        <span>Move</span>
                                                    </a>
                                                    <button class="delete-file-button flex items-center gap-3 px-2 py-2 text-sm text-gray-300 hover:text-white hover:bg-red-500/20 transition-all duration-200 w-full text-left" data-path="{{ currentPath ~ '/' ~ file.name }}" data-is-dir="{{ file.isDir ? 'true' : 'false' }}">
                                                        <i class="fas fa-trash text-red-400 w-4"></i>
                                                        <span>Delete</span>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-300 group-hover:text-gray-200">{{ file.size|e }}</td>
                                    <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-400 font-mono group-hover:text-gray-300">{{ file.permissions|e }}</td>
                                    <td class="px-2 py-2 whitespace-nowrap text-sm text-gray-400 group-hover:text-gray-300">{{ file.creationTime|e }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{# DELETE FILE CONFIRMATION MODAL #}
{% include "component/file-system/popup/detele-file-confirmation-popup.twig" %}

{# FILESYSTEM BROWSER FUNCTIONS #}
{{ encore_entry_script_tags('file-system-create-menu-js') }}
{{ encore_entry_script_tags('file-system-file-delete-js') }}
{{ encore_entry_script_tags('file-system-mobile-dropdown-js') }}
{% endblock %}
