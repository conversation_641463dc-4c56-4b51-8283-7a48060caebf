<div id="deletePopup" class="fixed inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center hidden z-50">
    <div class="bg-gray-800/95 border border-gray-700/50 rounded p-6 w-full max-w-md mx-4 shadow-2xl animate-popin">
        <div class="flex items-center gap-3 mb-4">
            <div class="w-8 h-8 bg-red-500/20 rounded-full flex items-center justify-center">
                <i class="fas fa-exclamation-triangle text-red-400"></i>
            </div>
            <h2 class="text-lg font-semibold text-white">Delete Exception File</h2>
        </div>
        <p class="text-gray-300 mb-6">Are you sure you want to delete this exception file? This action cannot be undone and all logged exception data will be permanently removed.</p>
        <div class="flex justify-end gap-3">
            <button id="cancelDeleteButton" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded font-medium transition-colors duration-200">Cancel</button>
            <button id="confirmDeleteButton" class="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded font-medium transition-colors duration-200">Delete File</button>
        </div>
    </div>
</div>
