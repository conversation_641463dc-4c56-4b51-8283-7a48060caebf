{% extends 'common/layout.twig' %}

{# FILE SYSTEM BROWSER COMPONENT - EDIT FILE #}
{% block component %}
<div class="flex flex-col h-full">
    {# SUB-NAVIGATION #}
    <div class="flex-shrink-0 px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
        <div class="flex items-center justify-between gap-2">
            <div class="flex items-center gap-3">
                <a href={{ path('app_file_system_view', { 'path': currentPath }) }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to file view">
                    <i class="fas fa-arrow-left text-gray-300 text-xs"></i>
                </a>
                <div class="hidden sm:block">
                    <h1 class="text-xm font-bold text-white">File Editor</h1>
                    <p class="text-gray-400 text-xs">Edit and save file content</p>
                </div>
            </div>
        </div>
    </div>

    {# MAIN CONTENT AREA #}
    <div class="flex-1 flex flex-col min-h-0">
        <div class="component flex flex-col h-full">
            {# FILESYSTEM PATH BREADCRUMB #}
            <div class="px-1 py-2 border-b border-gray-700/30 bg-gray-800/30">
                <div class="flex items-center space-x-1 px-1">
                    <i class="fas fa-file-code text-blue-400 mr-1"></i>
                    <div class="text-sm text-gray-100 flex items-center">
                        {% set parts = currentPath|split('/') %}
                        <a href={{ path('app_file_system_browser', { 'path': '/' }) }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">/base</a>
                        {% set accumulatedPath = '' %}
                        {% for part in parts|slice(0, parts|length - 1) %}
                            {% if part is not empty %}
                                {% set accumulatedPath = accumulatedPath ~ '/' ~ part %}
                                <span class="text-gray-500 mx-0">/</span>
                                <a href={{ path('app_file_system_browser', { 'path': accumulatedPath }) }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">{{ part }}</a>
                            {% endif %}
                        {% endfor %}
                        {% if parts|last is not empty %}
                            <span class="text-gray-500 mx-0">/</span>
                            <span class="text-white font-medium">{{ parts|last }}</span>
                        {% endif %}
                    </div>
                </div>
            </div>

            {# FILE EDITOR FORM #}
            <form action={{ path('app_file_system_save') }} method="post" class="flex flex-col flex-1">
                <input type="hidden" name="path" value="{{ currentPath|e }}">

                {# EDITOR HEADER BAR #}
                <div class="px-2 py-2 border-b border-gray-700/30 bg-gray-800/30">
                    <div class="flex items-center justify-between gap-4">
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 bg-green-500/20 rounded flex items-center justify-center">
                                <i class="fas fa-edit text-green-400 text-sm"></i>
                            </div>
                            <div>
                                <h2 class="text-lg font-semibold text-white">Editing: {{ parts|last }}</h2>
                                <p class="text-gray-400 text-xs">Use Ctrl+S to save</p>
                            </div>
                        </div>
                        <div class="flex items-center gap-2">
                            <button type="submit" class="inline-flex items-center gap-2 px-4 py-2 bg-green-500/20 hover:bg-green-500/30 text-green-300 hover:text-green-200 rounded border border-green-500/30 transition-all duration-200 text-sm font-medium">
                                <i class="fas fa-save text-xs"></i>
                                <span>Save</span>
                            </button>
                            <a href={{ path('app_file_system_view', { 'path': currentPath }) }} class="inline-flex items-center gap-2 px-4 py-2 bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white rounded border border-gray-600/30 transition-all duration-200 text-sm font-medium">
                                <i class="fas fa-times text-xs"></i>
                                <span>Cancel</span>
                            </a>
                        </div>
                    </div>
                </div>

                {# EDITOR TEXTAREA #}
                <div class="flex-1 relative overflow-hidden">
                    <textarea name="content" id="editor" class="w-full h-full bg-gray-900/30 text-sm text-green-400 font-mono p-4 border-none outline-none resize-none file-content-container" spellcheck="false" wrap="off">{{ fileContent|raw }}</textarea>
                </div>
            </form>
        </div>
    </div>
</div>

{# FILESYSTEM EDITOR FUNCTIONS #}
{{ encore_entry_script_tags('file-system-edit-js') }}
{% endblock %}
