{% extends 'common/layout.twig' %}

{# FILE SYSTEM BROWSER COMPONENT - RENAME FILE OR DIRECTORY #}
{% block component %}
<div class="flex flex-col h-full">
    {# SUB-NAVIGATION #}
    <div class="flex-shrink-0 px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
        <div class="flex items-center justify-between gap-2">
            <div class="flex items-center gap-3">
                <a href={{ path('app_file_system_browser', { 'path': directoryPath }) }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to directory">
                    <i class="fas fa-arrow-left text-gray-300 text-xs"></i>
                </a>
                <div class="hidden sm:block">
                    <h1 class="text-xm font-bold text-white">Rename {{ isDirectory ? 'Directory' : 'File' }}</h1>
                    <p class="text-gray-400 text-xs">Change the name of the selected item</p>
                </div>
            </div>
        </div>
    </div>

    {# MAIN CONTENT AREA #}
    <div class="flex-1 flex flex-col min-h-0">
        <div class="component">
            {# FILESYSTEM PATH BREADCRUMB #}
            <div class="px-1 py-2 border-b border-gray-700/30 bg-gray-800/30">
                <div class="flex items-center space-x-1 px-1">
                    <i class="fas fa-folder-open text-blue-400 mr-1"></i>
                    <div class="text-sm text-gray-100 flex items-center">
                        {% set parts = directoryPath|split('/') %}
                        <a href={{ path('app_file_system_browser', { 'path': '/' }) }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">/base</a>
                        {% set accumulatedPath = '' %}
                        {% for part in parts %}
                            {% if part is not empty %}
                                {% set accumulatedPath = accumulatedPath ~ '/' ~ part %}
                                <span class="text-gray-500 mx-0">/</span>
                                <a href={{ path('app_file_system_browser', { 'path': accumulatedPath }) }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">{{ part }}</a>
                            {% endif %}
                        {% endfor %}
                    </div>
                </div>
            </div>

            {# FLASH MESSAGES #}
            {% for label, messages in app.flashes %}
                {% for message in messages %}
                    <div class="mb-4 p-3 rounded {% if label == 'success' %}bg-green-500/20 text-green-300 border border-green-500/30{% else %}bg-red-500/20 text-red-300 border border-red-500/30{% endif %}">
                        <i class="fas fa-{% if label == 'success' %}check-circle{% else %}exclamation-circle{% endif %} mr-2"></i>{{ message|e }}
                    </div>
                {% endfor %}
            {% endfor %}

            {# RENAME FORM #}
            <form action={{ path('app_file_system_rename_save') }} method="post" class="max-w-2xl mx-auto sm:mt-10">
                <input type="hidden" name="path" value="{{ currentPath }}">

                <div class="bg-gray-800/50 border border-gray-700/50 sm:rounded overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-700/50 bg-gray-800/30">
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 {% if isDirectory %}bg-yellow-500/20{% else %}bg-blue-500/20{% endif %} rounded flex items-center justify-center">
                                {% if isDirectory %}
                                    <i class="fas fa-folder text-yellow-400 text-lg"></i>
                                {% else %}
                                    <i class="fas fa-file text-blue-400 text-lg"></i>
                                {% endif %}
                            </div>
                            <div>
                                <h2 class="text-xl font-bold text-white">Rename {{ isDirectory ? 'Directory' : 'File' }}</h2>
                                <p class="text-gray-400 text-sm">Change the name of "{{ currentName }}"</p>
                            </div>
                        </div>
                    </div>

                    <div class="p-6">
                        {# NEW NAME INPUT #}
                        <div class="mb-6">
                            <label for="newName" class="block text-sm font-medium text-gray-300 mb-3">New Name</label>
                            <input
                                type="text"
                                id="newName"
                                name="newName"
                                value="{{ currentName }}"
                                class="w-full bg-gray-900/50 text-white font-mono px-4 py-3 border border-gray-600/50 rounded focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 outline-none transition-all duration-200"
                                placeholder="Enter new name"
                                required
                                autofocus
                            >
                            <div class="mt-3 space-y-1">
                                <p class="text-gray-500 text-xs flex items-center">
                                    <i class="fas fa-info-circle mr-2 text-blue-400"></i>
                                    Path: <span class="text-gray-400 ml-1">{{ directoryPath }}</span>
                                </p>
                                <p class="text-gray-500 text-xs flex items-center">
                                    <i class="fas fa-exclamation-triangle mr-2 text-yellow-400"></i>
                                    Enter only the new name (without path separators)
                                </p>
                            </div>
                        </div>

                        {# FORM SUBMIT BUTTONS #}
                        <div class="flex items-center justify-between gap-4">
                            <a href={{ path('app_file_system_browser', { 'path': directoryPath }) }} class="inline-flex items-center gap-2 px-4 py-2 bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white rounded border border-gray-600/30 transition-all duration-200 text-sm font-medium">
                                <i class="fas fa-arrow-left text-xs"></i>
                                <span>Cancel</span>
                            </a>
                            <button type="submit" class="inline-flex items-center gap-2 px-6 py-2 bg-blue-500/20 hover:bg-blue-500/30 text-blue-300 hover:text-blue-200 rounded border border-blue-500/30 transition-all duration-200 text-sm font-medium">
                                <i class="fas fa-check text-xs"></i>
                                <span>Rename {{ isDirectory ? 'Directory' : 'File' }}</span>
                            </button>
                        </div>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

{# FILESYSTEM RENAME FUNCTIONS #}
{{ encore_entry_script_tags('file-system-rename-js') }}
{% endblock %}
