{% extends 'common/layout.twig' %}

{# FILE SYSTEM BROWSER COMPONENT - VIEW FILE #}
{% block component %}
<div class="flex flex-col h-full">
    {# SUB-NAVIGATION #}
    <div class="flex-shrink-0 px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
        {# GET DIRECTORY PATH #}
        {% set parts = currentPath|split('/') %}
        {% set directoryPath = parts|slice(0, parts|length - 1)|join('/') %}

        {# BACK BUTTON #}
        {% set referer = app.request.get('referer') %}
        <div class="flex items-center justify-between gap-2">
            <div class="flex items-center gap-3">
                {# BACK BUTTON #}
                {% if referer is empty %}
                    {% set backPath = path('app_file_system_browser', { 'path': directoryPath }) %}
                {% elseif referer == 'log_manager_exception_files' %}
                    {% set backPath = path('app_manager_logs_exception_files') %}
                {% elseif referer == 'log_manager_server_logs' %}
                    {% set backPath = path('app_manager_logs_system') %}
                {% elseif referer == 'app_system_audit' %}
                    {% set backPath = path('app_system_audit') %}
                {% elseif referer == 'monitoring_service_detail' %}
                    {% set backPath = path('app_manager_monitoring') %}
                {% else %}
                    {% set backPath = path('app_file_system_browser', { 'path': directoryPath }) %}
                {% endif %}
                <a href={{ backPath }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to previous page">
                    <i class="fas fa-arrow-left text-gray-300 text-xs"></i>
                </a>

                <div class="hidden sm:block">
                    <h1 class="text-xm font-bold text-white">{{ currentPath|split('/')|last }}</h1>
                    <p class="text-gray-400 text-xs">File viewer</p>
                </div>
            </div>

            <div class="flex items-center gap-2">
                {# DOWNLOAD BUTTON #}
                <a href={{ path('app_file_system_download', { 'path': currentPath }) }} class="w-8 h-8 bg-gray-700/50 hover:bg-green-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" id="loading-blocker" title="Download this file">
                    <i class="fas fa-download text-gray-300 text-xs"></i>
                </a>

                {# EDIT BUTTON (only for non-media files) #}
                {% if mediaType == 'non-mediafile' or mediaType == null %}
                    <a href={{ path('app_file_system_edit', { 'path': currentPath }) }} class="w-8 h-8 bg-gray-700/50 hover:bg-blue-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Edit this file">
                        <i class="fas fa-pen text-gray-300 text-xs"></i>
                    </a>
                {% endif %}

                {# MOVE BUTTON #}
                <a href={{ path('app_file_system_move', { 'path': currentPath }) }} class="w-8 h-8 bg-gray-700/50 hover:bg-purple-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Move this file">
                    <i class="fas fa-arrows-alt text-gray-300 text-xs"></i>
                </a>

                {# DELETE FILE #}
                {% if referer == 'log_manager_exception_files' %}
                    {% set service = app.request.get('service') %}
                    <button class="delete-button w-8 h-8 bg-gray-700/50 hover:bg-red-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" data-file={{ service }} title="Delete this exception file">
                        <i class="fas fa-trash text-gray-300 text-xs"></i>
                    </button>
                {% else %}
                    <button class="delete-file-button w-8 h-8 bg-gray-700/50 hover:bg-red-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" data-path="{{ currentPath }}" data-is-dir="false" title="Delete this file">
                        <i class="fas fa-trash text-gray-300 text-xs"></i>
                    </button>
                {% endif %}
            </div>
        </div>
    </div>

    {# MAIN CONTENT AREA #}
    <div class="flex-1 flex flex-col min-h-0">
        <div class="component">
            {# FILESYSTEM PATH BREADCRUMB #}
            <div class="px-1 py-2 border-b border-gray-700/30 bg-gray-800/30 overflow-x-auto">
                <div class="flex items-center space-x-1 px-1 whitespace-nowrap">
                    <i class="fas fa-file text-blue-400 mr-1"></i>
                    <div class="text-sm text-gray-100 flex items-center">
                        {% set parts = currentPath|split('/') %}
                        <a href={{ path('app_file_system_browser', { 'path': '/' }) }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">/base</a>
                        {% set accumulatedPath = '' %}
                        {% for part in parts|slice(0, parts|length - 1) %}
                            {% if part is not empty %}
                                {% set accumulatedPath = accumulatedPath ~ '/' ~ part %}
                                <span class="text-gray-500 mx-0">/</span>
                                <a href={{ path('app_file_system_browser', { 'path': accumulatedPath }) }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">{{ part }}</a>
                            {% endif %}
                        {% endfor %}
                        {% if parts|last is not empty %}
                            <span class="text-gray-500 mx-0">/</span>
                            <span class="text-white font-medium">{{ parts|last }}</span>
                        {% endif %}
                    </div>
                </div>
            </div>

            {# FLASH MESSAGES #}
            {% for label, messages in app.flashes %}
                {% for message in messages %}
                    <div class="p-2 {% if label == 'success' %}bg-green-600/20 text-green-400{% else %}bg-red-600/20 text-red-400 border border-red-700/50{% endif %}">
                        {{ message|e }}
                    </div>
                {% endfor %}
            {% endfor %}

            {# FILE METADATA AND NAVIGATION #}
            {% if mediaType == 'non-mediafile' and fileMetadata is defined and fileMetadata is not null %}
                <div class="px-2 py-3 border-b border-gray-700/30">
                    <div class="flex flex-wrap justify-between items-center text-sm">
                        <div class="flex items-center space-x-6">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-weight text-gray-400"></i>
                                <span class="text-gray-400">Size:</span>
                                <span class="text-white font-medium">{{ fileMetadata.formattedSize }}</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-list-ol text-gray-400"></i>
                                <span class="text-gray-400">Lines:</span>
                                <span class="text-white font-medium">{{ fileMetadata.totalLines }}</span>
                            </div>
                            {% if fileMetadata.isTruncated %}
                                <div class="flex items-center gap-2 hidden sm:flex">
                                    <i class="fas fa-info-circle text-yellow-400"></i>
                                    <span class="text-yellow-400 text-xs">
                                        Showing {{ fileMetadata.startLine }}-{{ fileMetadata.endLine }} of {{ fileMetadata.totalLines }}
                                    </span>
                                </div>
                            {% endif %}
                        </div>
                        {% if fileMetadata.isTruncated %}
                            <div class="flex items-center gap-1 mt-2 sm:mt-0">
                                {% if fileMetadata.startLine > 1 %}
                                    <a href={{ path('app_file_system_view', {'path': currentPath, 'start_line': 1, 'max_lines': fileMetadata.maxLines}) }} class="w-8 h-8 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 rounded flex items-center justify-center transition-all duration-200" title="First page">
                                        <i class="fas fa-angle-double-left text-blue-300 text-xs"></i>
                                    </a>
                                    <a href={{ path('app_file_system_view', {'path': currentPath, 'start_line': max(1, fileMetadata.startLine - fileMetadata.maxLines), 'max_lines': fileMetadata.maxLines}) }} class="w-8 h-8 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 rounded flex items-center justify-center transition-all duration-200" title="Previous page">
                                        <i class="fas fa-angle-left text-blue-300 text-xs"></i>
                                    </a>
                                {% endif %}
                                {% if fileMetadata.endLine < fileMetadata.totalLines %}
                                    <a href={{ path('app_file_system_view', {'path': currentPath, 'start_line': fileMetadata.endLine + 1, 'max_lines': fileMetadata.maxLines}) }} class="w-8 h-8 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 rounded flex items-center justify-center transition-all duration-200" title="Next page">
                                        <i class="fas fa-angle-right text-blue-300 text-xs"></i>
                                    </a>
                                    {% set lastPageStart = fileMetadata.totalLines - (fileMetadata.totalLines % fileMetadata.maxLines) %}
                                    {% if fileMetadata.totalLines % fileMetadata.maxLines == 0 %}
                                        {% set lastPageStart = fileMetadata.totalLines - fileMetadata.maxLines %}
                                    {% endif %}
                                    <a href={{ path('app_file_system_view', {'path': currentPath, 'start_line': max(1, lastPageStart + 1), 'max_lines': fileMetadata.maxLines}) }} class="w-8 h-8 bg-blue-500/20 hover:bg-blue-500/30 border border-blue-500/30 rounded flex items-center justify-center transition-all duration-200" title="Last page">
                                        <i class="fas fa-angle-double-right text-blue-300 text-xs"></i>
                                    </a>
                                {% endif %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% endif %}

            {# MEDIA FILE METADATA #}
            {% if mediaType and mediaType != 'non-mediafile' and fileMetadata is defined and fileMetadata is not null %}
                <div class="px-2 py-3 border-b border-gray-700/30">
                    <div class="flex flex-wrap justify-between items-center text-sm">
                        <div class="flex items-center space-x-6">
                            <div class="flex items-center gap-2">
                                <i class="fas fa-weight text-gray-400"></i>
                                <span class="text-gray-400">Size:</span>
                                <span class="text-white font-medium">{{ fileMetadata.formattedSize }}</span>
                            </div>
                            <div class="flex items-center gap-2">
                                <i class="fas fa-file-alt text-gray-400"></i>
                                <span class="text-gray-400">Type:</span>
                                <span class="text-white font-medium">{{ mediaType }}</span>
                            </div>
                            {% if mediaType starts with 'image/' %}
                                <div class="flex items-center gap-2">
                                    <i class="fas fa-expand-arrows-alt text-gray-400"></i>
                                    <span class="text-gray-400">Dimensions:</span>
                                    <span class="text-white font-medium" id="file-dimensions">Loading...</span>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                </div>
            {% endif %}

            {# FILE CONTENT VIEW #}
            <div class="flex-1 overflow-auto min-h-0">
                {# RAW FILE CONTENT VIEW #}
                {% if mediaType == 'non-mediafile' or mediaType == null %}
                    <div class="file-content-wrapper" style="min-width: 800px;">
                        <pre class="text-sm p-4 text-green-400 file-content bg-gray-900/30 w-full" data-path="{{ currentPath }}">{{ fileContent|e }}</pre>
                    </div>
                {% else %}
                    {# MEDIA FILE VIEWER #}
                    <div class="flex-1 flex flex-col">
                        {# IMAGE VIEWER #}
                        {% if mediaType starts with 'image/' %}
                            <div class="flex-1 flex flex-col bg-gray-900/20">
                                {# IMAGE CONTROLS BAR #}
                                <div class="px-2 py-1 border-b border-gray-700/30 bg-gray-800/30">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-4">
                                            <div class="flex items-center gap-2">
                                                <i class="fas fa-image text-blue-400"></i>
                                                <span class="text-sm font-medium text-gray-300">Image Viewer</span>
                                            </div>
                                            <div class="text-xs text-gray-400" id="image-info">
                                                Loading...
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <button onclick="resetImageZoom()" class="px-3 py-1 bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white rounded text-xs border border-gray-600/30 transition-all duration-200 phone-none">
                                                <i class="fas fa-search-minus mr-1"></i>
                                                Reset
                                            </button>
                                            <button onclick="toggleImageFullscreen()" class="px-3 py-1 bg-blue-500/20 hover:bg-blue-500/30 text-blue-300 hover:text-blue-200 rounded text-xs border border-blue-500/30 transition-all duration-200">
                                                <i class="fas fa-expand mr-1"></i>
                                                Fullscreen
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                {# IMAGE CONTAINER #}
                                <div class="p-4 overflow-hidden relative" id="image-container">
                                    <div class="w-full h-full flex items-center justify-center">
                                        <img src={{ path('app_file_system_get_resource', { 'path': currentPath }) }}
                                            alt={{ currentPath|e }}
                                            id="main-image"
                                            class="max-w-full max-h-full object-contain shadow-2xl rounded-lg border border-gray-600/30 cursor-zoom-in transition-transform duration-200"
                                            onload="updateImageInfo(this)">
                                    </div>

                                    {# LOADING SPINNER #}
                                    <div id="image-loading" class="absolute inset-0 flex items-center justify-center bg-gray-800/50 rounded-lg">
                                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"></div>
                                    </div>
                                </div>
                            </div>

                        {# VIDEO VIEWER #}
                        {% elseif mediaType starts with 'video/' %}
                            <div class="flex-1 flex flex-col bg-gray-900/20">
                                {# VIDEO CONTROLS BAR #}
                                <div class="px-2 py-1 border-b border-gray-700/30 bg-gray-800/30">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-4">
                                            <div class="flex items-center gap-2">
                                                <i class="fas fa-video text-purple-400"></i>
                                                <span class="text-sm font-medium text-gray-300">Video Player</span>
                                            </div>
                                            <div class="text-xs text-gray-400" id="video-info">
                                                {{ mediaType }}
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-2">
                                            <button onclick="toggleVideoFullscreen()" class="px-3 py-1 bg-purple-500/20 hover:bg-purple-500/30 text-purple-300 hover:text-purple-200 rounded text-xs border border-purple-500/30 transition-all duration-200">
                                                <i class="fas fa-expand mr-1"></i>
                                                Fullscreen
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                {# VIDEO CONTAINER #}
                                <div class="flex-1 flex items-center justify-center p-6">
                                    <div class="w-full max-w-6xl">
                                        <video id="main-video" controls preload="metadata"
                                               class="w-full shadow-2xl rounded-lg border border-gray-600/30 bg-black"
                                               onloadedmetadata="updateVideoInfo(this)"
                                               onerror="handleVideoError(this)"
                                               onwaiting="handleVideoWaiting(this)"
                                               oncanplay="handleVideoCanPlay(this)"
                                               onstalled="handleVideoStalled(this)">
                                            <source src={{ path('app_file_system_get_resource', { 'path': currentPath }) }} type={{ mediaType }}>
                                            <div class="p-4 text-center text-gray-400">
                                                <i class="fas fa-exclamation-triangle mb-2"></i>
                                                <p>Your browser does not support the video tag.</p>
                                            </div>
                                        </video>

                                        {# VIDEO STATUS INDICATOR #}
                                        <div id="video-status" class="hidden mt-2 text-center text-sm">
                                            <div class="inline-flex items-center gap-2 px-3 py-1 bg-gray-800/50 rounded">
                                                <div class="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-400"></div>
                                                <span class="text-gray-300">Loading...</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                        {# AUDIO VIEWER #}
                        {% elseif mediaType starts with 'audio/' %}
                            <div class="flex-1 flex flex-col bg-gray-900/20">
                                {# AUDIO CONTROLS BAR #}
                                <div class="px-2 py-1 border-b border-gray-700/30 bg-gray-800/30">
                                    <div class="flex items-center justify-between">
                                        <div class="flex items-center gap-4">
                                            <div class="flex items-center gap-2">
                                                <i class="fas fa-music text-green-400"></i>
                                                <span class="text-sm font-medium text-gray-300">Audio Player</span>
                                            </div>
                                            <div class="text-xs text-gray-400" id="audio-info">
                                                {{ mediaType }}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {# AUDIO CONTAINER #}
                                <div class="flex-1 flex items-center justify-center p-6">
                                    <div class="w-full max-w-2xl">
                                        {# AUDIO VISUALIZER PLACEHOLDER #}
                                        <div class="bg-gradient-to-r from-green-500/10 to-blue-500/10 rounded-lg p-8 mb-6 border border-gray-600/30">
                                            <div class="flex items-center justify-center mb-4">
                                                <div class="w-20 h-20 bg-green-500/20 rounded-full flex items-center justify-center">
                                                    <i class="fas fa-music text-green-400 text-2xl"></i>
                                                </div>
                                            </div>
                                            <div class="text-center">
                                                <h3 class="text-lg font-medium text-white mb-2">{{ currentPath|split('/')|last }}</h3>
                                                <p class="text-gray-400 text-sm" id="audio-duration">Loading...</p>
                                            </div>
                                        </div>

                                        {# AUDIO PLAYER #}
                                        <audio id="main-audio" controls preload="metadata"
                                               class="w-full shadow-lg rounded-lg border border-gray-600/30"
                                               onloadedmetadata="updateAudioInfo(this)">
                                            <source src={{ path('app_file_system_get_resource', { 'path': currentPath }) }} type={{ mediaType }}>
                                            <div class="p-4 text-center text-gray-400">
                                                <i class="fas fa-exclamation-triangle mb-2"></i>
                                                <p>Your browser does not support the audio tag.</p>
                                            </div>
                                        </audio>
                                    </div>
                                </div>
                            </div>
                        {% endif %}
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{# EXCEPTION FILE DELETE CONFIRMATION POPUP #}
{% if referer == 'log_manager_exception_files' %}
    {% include "component/file-system/popup/exception-file-delete-confirmation-popup.twig" %}
    {{ encore_entry_script_tags('exception-log-delete-confirmation-js') }}
{% endif %}

{# DELETE FILE CONFIRMATION POPUP #}
{% include "component/file-system/popup/detele-file-confirmation-popup.twig" %}
{{ encore_entry_script_tags('file-system-file-delete-js') }}

{# SYNTAX HIGHLIGHTING #}
{{ encore_entry_script_tags('file-system-syntax-highlight-js') }}

{# MEDIA VIEWER FUNCTIONALITY #}
{% if mediaType and mediaType != 'non-mediafile' %}
    {{ encore_entry_script_tags('file-system-media-viewer-js') }}
{% endif %}

{% endblock %}
