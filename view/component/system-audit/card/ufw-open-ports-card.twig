<div class="backdrop-blur-md bg-gray-800/50 border border-gray-700/50 rounded shadow-xl text-white w-full overflow-hidden">
    <div class="px-4 py-3 border-b border-gray-700/50 bg-gray-800/30">
        <h3 class="text-sm font-semibold text-white flex items-center">
            <i class="fas fa-shield-alt text-blue-400 mr-2"></i>
            Firewall Open Ports (UFW)
        </h3>
    </div>
    <div class="overflow-auto max-h-[320px] custom-scrollbar">
        {% if serviceManager.isUfwRunning() == false %}
            <div class="text-center text-gray-400 text-sm p-4">UFW is not running</div>
        {% else %}
            {% if ufwOpenPorts|length == 0 %}
                <div class="text-center text-gray-400 text-sm p-4">No open ports found</div>
            {% else %}
                <table class="min-w-full text-white text-sm">
                    <thead>
                        <tr class="border-b border-gray-700/50">
                            <th scope="col" class="px-4 py-3 text-left text-xs font-semibold uppercase tracking-wider text-gray-300 bg-gray-800/50">
                                Port
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-semibold uppercase tracking-wider text-gray-300 bg-gray-800/50">
                                Action
                            </th>
                            <th scope="col" class="px-4 py-3 text-left text-xs font-semibold uppercase tracking-wider text-gray-300 bg-gray-800/50">
                                From
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for port in ufwOpenPorts %}
                            <tr class="border-b border-gray-700/30 hover:bg-gray-700/30 transition-colors duration-200">
                                <td class="px-4 py-3 whitespace-nowrap font-medium">{{ port.port_service|e }}</td>
                                <td class="px-4 py-3 whitespace-nowrap text-gray-300">{{ port.action|e }}</td>
                                <td class="px-4 py-3 whitespace-nowrap text-gray-300">{{ port.from|e }}</td>
                            </tr>
                        {% endfor %}
                    </tbody>
                </table>
            {% endif %}
        {% endif %}
    </div>
</div>
