<div class="backdrop-blur-md bg-gray-800/50 border border-gray-700/50 rounded shadow-xl text-white w-full overflow-hidden">
	<div class="px-4 py-3 border-b border-gray-700/50 bg-gray-800/30">
		<h3 class="text-sm font-semibold text-white flex items-center">
			<i class="fas fa-users text-green-400 mr-2"></i>
			Linux System Users
		</h3>
	</div>
	<div class="overflow-auto max-h-[320px] custom-scrollbar">
		<table class="min-w-full text-white text-sm">
			<thead>
				<tr class="border-b border-gray-700/50">
					<th scope="col" class="px-4 py-3 text-left text-xs font-semibold uppercase tracking-wider text-gray-300 bg-gray-800/50">
						Username
					</th>
					<th scope="col" class="px-4 py-3 text-left text-xs font-semibold uppercase tracking-wider text-gray-300 bg-gray-800/50">
						Home
					</th>
					<th scope="col" class="px-4 py-3 text-left text-xs font-semibold uppercase tracking-wider text-gray-300 bg-gray-800/50">
						Shell
					</th>
					<th scope="col" class="px-4 py-3 text-left text-xs font-semibold uppercase tracking-wider text-gray-300 bg-gray-800/50">
						Locked
					</th>
					<th scope="col" class="px-4 py-3 text-left text-xs font-semibold uppercase tracking-wider text-gray-300 bg-gray-800/50">
						Sudo Permissions
					</th>
				</tr>
			</thead>
			<tbody>
				{% for user in linuxUsers %}
					<tr class="border-b border-gray-700/30 hover:bg-gray-700/30 transition-colors duration-200">
						<td class="px-4 py-3 whitespace-nowrap font-medium">
							<a href={{ path('app_file_system_view', { path: user.home ~ '/.bash_history', referer: 'app_system_audit' }) }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">{{ user.username|e }}</a>
						</td>
						<td class="px-4 py-3 whitespace-nowrap text-gray-300">{{ user.home|e }}</td>
						<td class="px-4 py-3 whitespace-nowrap text-gray-300">{{ user.shell|e }}</td>
						<td class="px-4 py-3 whitespace-nowrap {% if user.is_locked %}text-red-400{% else %}text-green-400{% endif %}">
							{{ user.is_locked ? 'Yes' : 'No' }}
						</td>
						<td class="px-4 py-3 whitespace-nowrap {% if user.has_sudo %}text-green-400{% else %}text-red-400{% endif %}">
							{{ user.has_sudo ? 'Yes' : 'No' }}
						</td>
					</tr>
				{% endfor %}
			</tbody>
		</table>
	</div>
</div>
