<div class="backdrop-blur-md bg-gray-800/50 border border-gray-700/50 rounded shadow-xl text-white w-full overflow-hidden">
	<div class="px-4 py-3 border-b border-gray-700/50 bg-gray-800/30">
		<h3 class="text-sm font-semibold text-white flex items-center">
			<i class="fas fa-terminal text-purple-400 mr-2"></i>
			SSH Access History
		</h3>
	</div>
	<div class="overflow-auto max-h-[320px] custom-scrollbar">
		{% if sshAccessHistory|length == 0 %}
			<div class="text-center text-gray-400 text-sm p-4">No SSH access history found</div>
		{% else %}
			<div class="space-y-1 p-2">
				{% for login in sshAccessHistory %}
					<div class="flex items-center justify-between p-3 bg-gray-700/30 hover:bg-gray-700/50 rounded transition-colors duration-200">
						<div class="flex flex-col">
							<div class="text-sm font-medium text-white">{{ login.user }}</div>
							<div class="text-xs text-green-400">{{ login.date }}</div>
						</div>
						<div class="text-right text-sm text-gray-300">
							<div class="font-medium">{{ login.host }}</div>
							<div class="text-xs text-gray-400">{{ login.ip }}:{{ login.port }}</div>
						</div>
					</div>
				{% endfor %}
			</div>
		{% endif %}
	</div>
</div>
