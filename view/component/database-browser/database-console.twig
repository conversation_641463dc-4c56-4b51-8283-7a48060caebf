{% extends 'common/layout.twig' %}

{# DATABASE BROWSER COMPONENT - DATABASE QUERY CONSOLE #}
{% block component %}
<div class="flex flex-col h-full">
    {# SUB-NAVIGATION #}
    <div class="flex-shrink-0 px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
        <div class="flex items-center justify-between gap-2">
            <div class="flex items-center gap-3">
                <a href={{ path('app_manager_database') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to database browser">
                    <i class="fas fa-arrow-left text-gray-300 text-xs"></i>
                </a>
                <div class="hidden sm:block">
                    <h1 class="text-xm font-bold text-white">Database Console</h1>
                    <p class="text-gray-400 text-xs">Execute SQL queries</p>
                </div>
            </div>
        </div>
    </div>

    {# BREADCRUMB PANEL #}
    <div class="px-2 py-1 bg-gray-800/30 border-b border-gray-700/50">
        <div class="flex items-center gap-2 text-sm">
            <i class="fas fa-database text-gray-400 text-xs"></i>
            <a href={{ path('app_manager_database') }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">Databases</a>
            <i class="fas fa-chevron-right text-gray-500 text-xs"></i>
            <span class="text-gray-300">Query Console</span>
        </div>
    </div>

    {# MAIN CONTENT AREA #}
    <div class="flex-1 flex flex-col min-h-0">
        <div class="database-browser md:p-4 component">
            <div class="max-w-4xl mx-auto space-y-4">

                {# OUTPUT BOX #}
                {% if output is not null %}
                    <div class="bg-gray-800/50 border border-gray-700/50 sm:rounded">
                        <div class="bg-gray-700/50 px-4 py-3 border-b border-gray-600/50">
                            <h3 class="text-lg font-semibold text-gray-200">Database Output</h3>
                        </div>
                        <div class="p-4">
                            <pre id="query-output" class="text-sm whitespace-pre-wrap break-words text-gray-300 font-mono">{{ output|raw }}</pre>
                        </div>
                    </div>
                {% endif %}

                {# QUERY FORM #}
                {% include "component/database-browser/form/query-form.twig" %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
