{% extends 'common/layout.twig' %}

{# DATABASE BROWSER COMPONENT - TABLE DATA BROWSER #}
{% block component %}
<div class="flex flex-col h-full">
    {# SUB-NAVIGATION #}
    <div class="flex-shrink-0 px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
        <div class="flex items-center justify-between gap-2">
            <div class="flex items-center gap-3">
                <a href={{ path('app_manager_database', {'database': databaseName}) }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to database browser">
                    <i class="fas fa-arrow-left text-gray-300 text-xs"></i>
                </a>
                <div class="hidden sm:block">
                    <h1 class="text-xm font-bold text-white">{{ tableName|e }}</h1>
                    <p class="text-gray-400 text-xs">{{ tableDataCount }} rows found</p>
                </div>
            </div>

            <div class="flex items-center gap-2">
                {# QUICK ACTIONS #}
                <a href={{ path('app_manager_database_add', {'database': databaseName, 'table': tableName}) }} class="w-8 h-8 bg-green-500/20 hover:bg-green-500/30 rounded flex items-center justify-center transition-all duration-200 border border-green-500/30" title="Add new record to table">
                    <i class="fas fa-plus text-green-400 text-xs"></i>
                </a>
                <a href={{ path('app_manager_database_truncate', {'database': databaseName, 'table': tableName}) }} class="w-8 h-8 bg-red-500/20 hover:bg-red-500/30 rounded flex items-center justify-center transition-all duration-200 border border-red-500/30" title="Drop all records from table">
                    <i class="fas fa-trash text-red-400 text-xs"></i>
                </a>
            </div>
        </div>
    </div>

    {# BREADCRUMB PANEL #}
    <div class="px-2 py-1 bg-gray-800/30 border-b border-gray-700/50">
        <div class="flex items-center gap-2 text-sm">
            <i class="fas fa-database text-gray-400 text-xs"></i>
            <a href={{ path('app_manager_database') }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">Databases</a>
            <i class="fas fa-chevron-right text-gray-500 text-xs"></i>
            <a href={{ path('app_manager_database', {'database': databaseName}) }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">{{ databaseName|e }}</a>
            <i class="fas fa-chevron-right text-gray-500 text-xs"></i>
            <span class="text-gray-300">{{ tableName|e }}</span>
        </div>
    </div>

    {# MAIN CONTENT AREA #}
    <div class="flex-1 flex flex-col min-h-0">
        {% if tableData|length == 0 %}
            <div class="flex flex-col items-center justify-center flex-1">
                <div class="w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mb-4">
                    <i class="fas fa-database text-gray-400 text-2xl"></i>
                </div>
                <h3 class="text-xl font-bold text-white mb-2">No records found</h3>
                <p class="text-gray-400 text-center">This table contains no data</p>
            </div>
        {% else %}
            {# TABLE VIEW #}
            <div class="overflow-x-auto">
                <table class="min-w-full text-white w-full bg-gray-800/50 border border-gray-700/50" style="min-width: 1200px;">
                        <thead>
                            <tr class="bg-gray-700/50">
                                {# TABLE HEADER #}
                                {% for column in tableData[0]|keys %}
                                    <th class="px-4 py-3 border-b border-gray-600/50 text-left font-semibold text-gray-200 whitespace-nowrap">{{ column }}</th>
                                {% endfor %}

                                {# EDIT AND DELETE BUTTONS #}
                                {% if 'id' in tableData[0]|keys %}
                                    <th class="px-4 py-3 border-b border-gray-600/50 text-center font-semibold text-gray-200 whitespace-nowrap">
                                        <i class="fas fa-edit"></i>
                                    </th>
                                    <th class="px-4 py-3 border-b border-gray-600/50 text-center font-semibold text-gray-200 whitespace-nowrap">
                                        <i class="fas fa-trash"></i>
                                    </th>
                                {% endif %}
                            </tr>
                        </thead>
                        <tbody>
                            {# TABLE ROW #}
                            {% for row in tableData %}
                                <tr class="hover:bg-gray-600/40 hover:shadow-lg hover:shadow-gray-900/20 transition-all duration-300">
                                    {% for column in row %}
                                        <td class="px-4 py-2 border-b border-gray-600/60 text-left relative">
                                            <div class="flex items-center">
                                                <span class="truncate max-w-xl text-sm">{{ column|slice(0, 50) ~ (column|length > 50 ? '' : '') }}</span>
                                                {% if column|length > 50 %}
                                                    <button class="text-blue-400 hover:text-blue-300 text-xs font-bold view-raw-button ml-2" data-fulltext={{ column|e('html_attr') }}>...</button>
                                                {% endif %}
                                            </div>
                                        </td>
                                    {% endfor %}

                                    {# EDIT AND DELETE BUTTONS #}
                                    {% if row.id|default(null) is not null %}
                                        <td class="px-4 py-2 border-b border-gray-600/60 text-center">
                                            <div class="flex justify-center">
                                                <a href={{ path('app_manager_database_edit', {'database': databaseName, 'table': tableName, 'page': currentPage, 'id': row.id}) }} class="w-6 h-6 bg-yellow-500/20 hover:bg-yellow-500/30 rounded flex items-center justify-center transition-all duration-200 border border-yellow-500/30" title="Edit record">
                                                    <i class="fas fa-edit text-yellow-400 text-xs"></i>
                                                </a>
                                            </div>
                                        </td>
                                        <td class="px-4 py-2 border-b border-gray-600/60 text-center">
                                            <div class="flex justify-center">
                                                <button class="w-6 h-6 bg-red-500/20 hover:bg-red-500/30 rounded flex items-center justify-center transition-all duration-200 border border-red-500/30 delete-button" data-url={{ path('app_manager_database_delete', {'database': databaseName, 'table': tableName, 'page': currentPage, 'id': row.id}) }} title="Delete record">
                                                    <i class="fas fa-trash text-red-400 text-xs"></i>
                                                </button>
                                            </div>
                                        </td>
                                    {% endif %}
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
            </div>
        {% endif %}

        {# PAGINATION SYSTEM #}
        {% if tableDataCount > limitPerPage %}
            {% set totalPages = (tableDataCount / limitPerPage)|round(0, 'ceil') %}
            {% set previousPage = currentPage > 1 ? currentPage - 1 : 1 %}
            {% set nextPage = currentPage < totalPages ? currentPage + 1 : totalPages %}
            {% set startPage = max(currentPage - 1, 1) %}
            {% set endPage = min(currentPage + 1, totalPages) %}
            {% set startPageDesktop = max(currentPage - 3, 1) %}
            {% set endPageDesktop = min(currentPage + 3, totalPages) %}

            <div class="flex flex-col sm:flex-row justify-center items-center py-4 border-t border-gray-700/50 bg-gray-900 gap-3">
                <div class="flex items-center gap-2">

                    {# FIRST PAGE - Hidden on mobile #}
                    <div class="hidden sm:block">
                        {% if currentPage == 1 %}
                            <span class="px-3 py-2 text-sm font-medium text-gray-500 bg-gray-700/30 border border-gray-600/30 rounded cursor-not-allowed opacity-50">
                                <i class="fas fa-angle-double-left text-xs"></i>
                            </span>
                        {% else %}
                            <a href={{ path('app_manager_database_table_browser', {'database': databaseName, 'table': tableName, 'page': 1}) }} class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-700/50 border border-gray-600/50 rounded hover:bg-gray-600/50 hover:text-white transition-all duration-200" title="First page">
                                <i class="fas fa-angle-double-left text-xs"></i>
                            </a>
                        {% endif %}
                    </div>

                    {# PREVIOUS PAGE #}
                    {% if currentPage == 1 %}
                        <span class="px-3 py-2 text-sm font-medium text-gray-500 bg-gray-700/30 border border-gray-600/30 rounded cursor-not-allowed opacity-50">
                            <i class="fas fa-chevron-left text-xs"></i>
                        </span>
                    {% else %}
                        <a href={{ path('app_manager_database_table_browser', {'database': databaseName, 'table': tableName, 'page': previousPage}) }} class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-700/50 border border-gray-600/50 rounded hover:bg-gray-600/50 hover:text-white transition-all duration-200">
                            <i class="fas fa-chevron-left text-xs"></i>
                        </a>
                    {% endif %}

                    {# PAGE NUMBERS - Mobile version (fewer pages) #}
                    <div class="flex items-center gap-2 sm:hidden">
                        {% for page in startPage..endPage %}
                            <a href={{ path('app_manager_database_table_browser', {'database': databaseName, 'table': tableName, 'page': page}) }} class="px-3 py-2 text-sm font-medium {{ page == currentPage ? 'text-white bg-blue-500/20 border-blue-500/50' : 'text-gray-300 bg-gray-700/50 border-gray-600/50 hover:bg-gray-600/50 hover:text-white' }} border rounded transition-all duration-200">
                                {{ page }}
                            </a>
                        {% endfor %}
                    </div>

                    {# PAGE NUMBERS - Desktop version (more pages) #}
                    <div class="hidden sm:flex items-center gap-2">
                        {% for page in startPageDesktop..endPageDesktop %}
                            <a href={{ path('app_manager_database_table_browser', {'database': databaseName, 'table': tableName, 'page': page}) }} class="px-3 py-2 text-sm font-medium {{ page == currentPage ? 'text-white bg-blue-500/20 border-blue-500/50' : 'text-gray-300 bg-gray-700/50 border-gray-600/50 hover:bg-gray-600/50 hover:text-white' }} border rounded transition-all duration-200">
                                {{ page }}
                            </a>
                        {% endfor %}
                    </div>

                    {# NEXT PAGE #}
                    {% if currentPage == totalPages %}
                        <span class="px-3 py-2 text-sm font-medium text-gray-500 bg-gray-700/30 border border-gray-600/30 rounded cursor-not-allowed opacity-50">
                            <i class="fas fa-chevron-right text-xs"></i>
                        </span>
                    {% else %}
                        <a href={{ path('app_manager_database_table_browser', {'database': databaseName, 'table': tableName, 'page': nextPage}) }} class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-700/50 border border-gray-600/50 rounded hover:bg-gray-600/50 hover:text-white transition-all duration-200">
                            <i class="fas fa-chevron-right text-xs"></i>
                        </a>
                    {% endif %}

                    {# LAST PAGE - Hidden on mobile #}
                    <div class="hidden sm:block">
                        {% if currentPage == totalPages %}
                            <span class="px-3 py-2 text-sm font-medium text-gray-500 bg-gray-700/30 border border-gray-600/30 rounded cursor-not-allowed opacity-50">
                                <i class="fas fa-angle-double-right text-xs"></i>
                            </span>
                        {% else %}
                            <a href={{ path('app_manager_database_table_browser', {'database': databaseName, 'table': tableName, 'page': totalPages}) }} class="px-3 py-2 text-sm font-medium text-gray-300 bg-gray-700/50 border border-gray-600/50 rounded hover:bg-gray-600/50 hover:text-white transition-all duration-200" title="Last page">
                                <i class="fas fa-angle-double-right text-xs"></i>
                            </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        {% endif %}
        </div>
    </div>
</div>

{# RAW TEXT POPUP #}
{% include "component/database-browser/popup/raw-data-view-popup.twig" %}

{# DELETE CONFIRMATION POPUP #}
{% include "component/database-browser/popup/delete-confirmation-popup.twig" %}

{# POPUP FUNCTIONS #}
{{ encore_entry_script_tags('database-table-browser-js') }}
{% endblock %}
