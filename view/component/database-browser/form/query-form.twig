{# DATABASE BROWSER COMPONENT - QUERY FORM #}
<div class="bg-gray-800/50 border border-gray-700/50 sm:rounded">
    <div class="bg-gray-700/50 px-4 py-3 border-b border-gray-600/50">
        <h3 class="text-lg font-semibold text-gray-200">SQL Query</h3>
    </div>
    <div class="p-4">
        {{ form_start(queryForm, {'attr': {'class': 'space-y-4'}}) }}
            <div>
                {{ form_row(queryForm.query, {
                    'label_attr': {'class': 'block text-sm font-medium text-gray-200 mb-2'},
                    'attr': {
                        'class': 'mt-1 block w-full p-4 rounded bg-gray-600/20 border border-gray-600/50 text-white focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 resize-none outline-none transition-all duration-200',
                        'style': 'resize: none; min-height: 120px;'
                    }})
                }}
            </div>

            {# FORM SUBMIT BUTTON #}
            <div class="text-right">
                <button type="submit" class="bg-blue-600 hover:bg-blue-700 rounded text-white font-semibold py-3 px-6 focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-all duration-200">
                    Execute Query
                </button>
            </div>
        {{ form_end(queryForm) }}
    </div>
</div>
