{% extends 'common/layout.twig' %}

{# DATABASE BROWSER COMPONENT - ADD ROW TO TABLE #}
{% block component %}
<div class="flex flex-col h-full">
    {# SUB-NAVIGATION #}
    <div class="flex-shrink-0 px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
        <div class="flex items-center justify-between gap-2">
            <div class="flex items-center gap-3">
                <a href={{ path('app_manager_database_table_browser', {'database': databaseName, 'table': tableName}) }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to table browser">
                    <i class="fas fa-arrow-left text-gray-300 text-xs"></i>
                </a>
                <div class="hidden sm:block">
                    <h1 class="text-xm font-bold text-white">Add Row</h1>
                    <p class="text-gray-400 text-xs">{{ tableName|e }}</p>
                </div>
            </div>
        </div>
    </div>

    {# BREADCRUMB PANEL #}
    <div class="py-1 bg-gray-800/30 border-b border-gray-700/50">
        <div class="flex items-center gap-2 text-sm">
            <i class="fas fa-database text-gray-400 text-xs"></i>
            <a href={{ path('app_manager_database') }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">Databases</a>
            <i class="fas fa-chevron-right text-gray-500 text-xs"></i>
            <a href={{ path('app_manager_database', {'database': databaseName}) }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">{{ databaseName|e }}</a>
            <i class="fas fa-chevron-right text-gray-500 text-xs"></i>
            <a href={{ path('app_manager_database_table_browser', {'database': databaseName, 'table': tableName}) }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">{{ tableName|e }}</a>
            <i class="fas fa-chevron-right text-gray-500 text-xs"></i>
            <span class="text-gray-300">Add Row</span>
        </div>
    </div>

    {# MAIN CONTENT AREA #}
    <div class="flex-1 flex flex-col min-h-0">
        <div class="database-browser md:p-4 component">
            <div class="max-w-4xl mx-auto">
                <div class="bg-gray-800/50 border border-gray-700/50 sm:rounded">
                    <div class="bg-gray-700/50 px-4 py-3 border-b border-gray-600/50">
                        <h2 class="text-xl font-semibold text-gray-200">Add Row to {{ tableName|e }}</h2>
                    </div>
                    <div class="p-4">

                        {# ERROR BOX #}
                        {% if errors is not empty %}
                            <div class="bg-red-500/20 border border-red-500/50 rounded text-white p-4 mb-4">
                                <h3 class="font-semibold text-red-400 mb-2">There were some errors:</h3>
                                <ul class="list-disc ml-5 text-red-300">
                                    {% for error in errors %}
                                        <li>{{ error }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        {% endif %}

                        {# FORM INPUTS #}
                        <form method="post" action={{ path('app_manager_database_add', {'database': databaseName, 'table': tableName}) }}>
                            <div class="space-y-4">
                                {% for column in columns %}
                                    <div>
                                        <label class="block text-sm font-medium text-gray-200 mb-2" for={{ column.COLUMN_NAME|e }}>{{ column.COLUMN_NAME|capitalize|e }}</label>
                                        {% if column.COLUMN_TYPE == 'datetime' %}
                                            <input
                                                class="w-full px-4 py-3 rounded bg-gray-700/50 border border-gray-600/50 text-white focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 outline-none transition-all duration-200"
                                                type="datetime-local"
                                                name={{ column.COLUMN_NAME|e }}
                                                id={{ column.COLUMN_NAME|e }}
                                                value={{ formData[column.COLUMN_NAME]|default('')|e }}
                                            >
                                        {% else %}
                                            <input
                                                class="w-full px-4 py-3 rounded bg-gray-700/50 border border-gray-600/50 text-white focus:border-blue-500/50 focus:ring-2 focus:ring-blue-500/20 outline-none transition-all duration-200"
                                                type="text"
                                                name={{ column.COLUMN_NAME|e }}
                                                id={{ column.COLUMN_NAME|e }}
                                                value={{ formData[column.COLUMN_NAME]|default('')|e }}
                                            >
                                        {% endif %}
                                    </div>
                                {% endfor %}
                            </div>
                            <div class="mt-6 text-right">
                                <button class="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded focus:outline-none focus:ring-2 focus:ring-blue-500/50 transition-all duration-200" type="submit">Add Row</button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
