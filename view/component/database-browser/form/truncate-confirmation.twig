{% extends 'common/layout.twig' %}

{# DATABASE BROWSER COMPONENT - TABLE TRUNCATE CONFIRMATION #}
{% block component %}
<div class="flex flex-col h-full">
    {# SUB-NAVIGATION #}
    <div class="flex-shrink-0 px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
        <div class="flex items-center justify-between gap-2">
            <div class="flex items-center gap-3">
                <a href={{ path('app_manager_database_table_browser', {'database': databaseName, 'table': tableName}) }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to table browser">
                    <i class="fas fa-arrow-left text-gray-300 text-xs"></i>
                </a>
                <div class="hidden sm:block">
                    <h1 class="text-xm font-bold text-white">Truncate Table</h1>
                    <p class="text-gray-400 text-xs">{{ tableName|e }}</p>
                </div>
            </div>
        </div>
    </div>

    {# BREADCRUMB PANEL #}
    <div class="py-1 bg-gray-800/30 border-b border-gray-700/50">
        <div class="flex items-center gap-2 text-sm">
            <i class="fas fa-database text-gray-400 text-xs"></i>
            <a href={{ path('app_manager_database') }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">Databases</a>
            <i class="fas fa-chevron-right text-gray-500 text-xs"></i>
            <a href={{ path('app_manager_database', {'database': databaseName}) }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">{{ databaseName|e }}</a>
            <i class="fas fa-chevron-right text-gray-500 text-xs"></i>
            <a href={{ path('app_manager_database_table_browser', {'database': databaseName, 'table': tableName}) }} class="text-blue-400 hover:text-blue-300 transition-colors duration-200">{{ tableName|e }}</a>
            <i class="fas fa-chevron-right text-gray-500 text-xs"></i>
            <span class="text-gray-300">Truncate</span>
        </div>
    </div>

    {# MAIN CONTENT AREA #}
    <div class="flex-1 flex flex-col min-h-0">
        <div class="database-browser md:p-4 component">
            <div class="max-w-lg mx-auto">
                <div class="bg-gray-800/50 border border-gray-700/50 sm:rounded">
                    <div class="bg-red-500/20 border-b border-red-500/30 px-6 py-4">
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 bg-red-500/30 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation-triangle text-red-400"></i>
                            </div>
                            <h2 class="text-xl font-semibold text-white">Truncate Table</h2>
                        </div>
                    </div>
                    <div class="p-6">
                        <p class="text-gray-300 mb-2">
                            Are you sure you want to delete <strong class="text-white">all data</strong> from the table:
                        </p>
                        <p class="text-center text-xl font-semibold text-blue-400 mb-4">{{ tableName|e }}</p>
                        <div class="bg-yellow-500/10 border border-yellow-500/30 rounded p-4 mb-6">
                            <p class="text-yellow-300 text-sm">
                                <i class="fas fa-warning text-yellow-400 mr-2"></i>
                                <strong>Warning:</strong> This action cannot be undone. All data in this table will be permanently deleted.
                            </p>
                        </div>
                        <div class="flex justify-center gap-4">
                            <a href={{ path('app_manager_database_table_browser', {'database': databaseName, 'table': tableName}) }} class="px-6 py-3 bg-gray-600 hover:bg-gray-700 text-white rounded font-semibold transition-colors duration-200">
                                Cancel
                            </a>
                            <a href={{ path('app_manager_database_truncate', {'database': databaseName, 'table': tableName, 'confirm': 'yes'}) }} class="px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded font-semibold transition-colors duration-200">
                                Truncate Table
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
