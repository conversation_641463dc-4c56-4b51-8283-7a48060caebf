{% extends 'common/layout.twig' %}

{# ACCOUNT SETTINGS TABLE COMPONENT #}
{% block component %}
{# SUB NAVIGATION #}
<div class="px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
    <div class="flex items-center gap-4">
        <a href={{ path('app_dashboard') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to dashboard">
            <i class="fas fa-arrow-left text-gray-300 text-sm"></i>
        </a>
        <div>
            <h1 class="text-xm font-bold text-white">Account Settings</h1>
            <p class="text-gray-400 text-sm">Manage your account preferences and security</p>
        </div>
    </div>
</div>

{# ACCOUNT SETTINGS TABLE #}
<div class="sm:p-6">
    <div class="w-full max-w-4xl mx-auto">
        <div class="backdrop-blur-md bg-gray-800/50 sm:border-t sm:border-l sm:border-r border-b border-gray-700/50 sm:rounded shadow-xl text-white w-full overflow-hidden">
            <div class="p-2 sm:p-4">
                <div class="space-y-2 sm:space-y-4">

                    {# PROFILE PICTURE SETTINGS COMPONENT #}
                    <div class="bg-gray-700/30 rounded p-4 border border-gray-600/30 hover:bg-gray-700/40 transition-all duration-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-8 h-8 bg-blue-500/20 rounded flex items-center justify-center">
                                    <i class="fas fa-user-circle text-blue-400 text-sm"></i>
                                </div>
                                <div>
                                    <div class="text-white font-medium text-sm">Profile Picture</div>
                                    <div class="text-gray-400 text-xs">Your account profile image</div>
                                </div>
                            </div>
                            <div class="flex items-center gap-4">
                                {% if getUserData().getProfilePic == 'default_pic' %}
                                    <img class="rounded-full h-10 w-10 object-cover border-2 border-gray-600/50" src={{ asset('/assets/images/default-profile.jpg') }} alt="profile picture">
                                {% else %}
                                    <img class="rounded-full h-10 w-10 object-cover border-2 border-gray-600/50" src="data:image/jpeg;base64,{{ getUserData().getProfilePic|e }}" alt="profile picture">
                                {% endif %}
                                <a href={{ path('app_account_settings_change_picture') }} class="px-3 py-1.5 bg-blue-500/20 hover:bg-blue-500/30 text-blue-300 rounded transition-all duration-200 border border-blue-500/30 text-sm font-medium">
                                    <i class="fas fa-edit mr-1"></i>
                                    <span class="hidden sm:inline">Change</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    {# USERNAME SETTINGS COMPONENT #}
                    <div class="bg-gray-700/30 rounded p-4 border border-gray-600/30 hover:bg-gray-700/40 transition-all duration-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-8 h-8 bg-green-500/20 rounded flex items-center justify-center">
                                    <i class="fas fa-user text-green-400 text-sm"></i>
                                </div>
                                <div>
                                    <div class="text-white font-medium text-sm">Username</div>
                                    <div class="text-gray-400 text-xs">Your unique account identifier</div>
                                </div>
                            </div>
                            <div class="flex items-center gap-4">
                                <div class="text-gray-300 font-mono text-sm bg-gray-800/50 px-3 py-1.5 rounded border border-gray-600/30">
                                    {{ getUserData().username|e }}
                                </div>
                                <a href={{ path('app_account_settings_change_username') }} class="px-3 py-1.5 bg-green-500/20 hover:bg-green-500/30 text-green-300 rounded transition-all duration-200 border border-green-500/30 text-sm font-medium">
                                    <i class="fas fa-edit mr-1"></i>
                                    <span class="hidden sm:inline">Change</span>
                                </a>
                            </div>
                        </div>
                    </div>

                    {# PASSWORD SETTINGS COMPONENT #}
                    <div class="bg-gray-700/30 rounded p-4 border border-gray-600/30 hover:bg-gray-700/40 transition-all duration-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-3">
                                <div class="w-8 h-8 bg-orange-500/20 rounded flex items-center justify-center">
                                    <i class="fas fa-lock text-orange-400 text-sm"></i>
                                </div>
                                <div>
                                    <div class="text-white font-medium text-sm">Password</div>
                                    <div class="text-gray-400 text-xs">Your account security password</div>
                                </div>
                            </div>
                            <div class="flex items-center gap-4">
                                <div class="text-gray-400 font-mono text-sm bg-gray-800/50 px-3 py-1.5 rounded border border-gray-600/30">
                                    ••••••••••
                                </div>
                                <a href={{ path('app_account_settings_change_password') }} class="px-3 py-1.5 bg-orange-500/20 hover:bg-orange-500/30 text-orange-300 rounded transition-all duration-200 border border-orange-500/30 text-sm font-medium">
                                    <i class="fas fa-edit mr-1"></i>
                                    <span class="hidden sm:inline">Change</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    {# PUSH NOTIFICATIONS SETTINGS #}
    {% if pushNotificationsEnabled == true %}
        <div class="component sm:pt-6">
            <div class="w-full max-w-4xl mx-auto">
                <div class="backdrop-blur-md bg-gray-800/50 border border-gray-700/50 sm:rounded shadow-xl text-white w-full overflow-hidden">
                    <div class="px-6 py-4 border-b border-gray-700/50 bg-gray-800/30">
                        <div class="flex items-center gap-3">
                            <div class="w-8 h-8 bg-purple-500/20 rounded flex items-center justify-center">
                                <i class="fas fa-bell text-purple-400 text-lg"></i>
                            </div>
                            <div>
                                <h2 class="text-xl font-bold text-white">Push Notifications</h2>
                                <p class="text-gray-400 text-sm">Manage your notification preferences</p>
                            </div>
                        </div>
                    </div>
                    <div class="p-2 sm:p-6">
                        <div class="bg-gray-700/30 rounded p-4 border border-gray-600/30">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-3">
                                    <div class="w-8 h-8 bg-purple-500/20 rounded flex items-center justify-center">
                                        <i class="fas fa-bell text-purple-400 text-sm"></i>
                                    </div>
                                    <div>
                                        <div class="text-white font-medium text-sm">Subscription Status</div>
                                        <div class="text-gray-400 text-xs">Current notification subscription state</div>
                                    </div>
                                </div>
                                <div class="flex items-center gap-4">
                                    <span id="push-status" class="text-green-400 font-medium text-sm bg-gray-800/50 px-3 py-1.5 rounded border border-gray-600/30">checking...</span>
                                    <button id="subscribe-btn" class="hidden px-4 py-2 bg-purple-500/20 hover:bg-purple-500/30 text-purple-300 rounded transition-all duration-200 border border-purple-500/30 text-sm font-medium">
                                        <i class="fas fa-bell mr-2"></i>
                                        Subscribe
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        {# REGISTER NOTIFICATIONS SETTINGS JAVASCRIPT #}
        {{ encore_entry_script_tags('notifications-settings-js') }}
    {% endif %}
</div>
{% endblock %}
