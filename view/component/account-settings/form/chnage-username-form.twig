{% extends 'common/layout.twig' %}

{# ACCOUNT SETTING CHANGE USERNAME #}
{% block component %}
{# SUB NAVIGATION #}
<div class="px-2 py-1 border-b border-gray-700/50 bg-gray-800/30">
    <div class="flex items-center gap-4">
        <a href={{ path('app_account_settings_table') }} class="w-8 h-8 bg-gray-700/50 hover:bg-gray-600/50 rounded flex items-center justify-center transition-all duration-200 border border-gray-600/30" title="Back to account settings">
            <i class="fas fa-arrow-left text-gray-300 text-sm"></i>
        </a>
        <div>
            <h1 class="text-l font-bold text-white">Change Username</h1>
            <p class="text-gray-400 text-sm">Update your account username</p>
        </div>
    </div>
</div>

{# USERNAME CHANGE FORM #}
<div class="component p-6">
    <div class="w-full max-w-md mx-auto">
        <div class="backdrop-blur-md bg-gray-800/50 border border-gray-700/50 rounded shadow-xl text-white w-full overflow-hidden">
            <div class="px-6 py-4 border-b border-gray-700/50 bg-gray-800/30">
                <div class="flex items-center gap-3">
                    <div class="w-8 h-8 bg-green-500/20 rounded flex items-center justify-center">
                        <i class="fas fa-user text-green-400 text-lg"></i>
                    </div>
                    <div>
                        <h2 class="text-xm font-bold text-white">Username Update</h2>
                        <p class="text-gray-400 text-sm">Enter your new username</p>
                    </div>
                </div>
            </div>
            <div class="p-6">
                {# SUCCESS MESSAGE BOX #}
                {% for message in app.flashes('success') %}
                    <div class="bg-green-500/20 border border-green-500/30 text-green-300 p-3 mb-4 rounded text-center animate-popin">
                        <i class="fas fa-check-circle mr-2"></i>{{ message|e }}
                    </div>
                {% endfor %}

                {# ERROR MESSAGE BOX #}
                {% for message in app.flashes('error') %}
                    <div class="bg-red-500/20 border border-red-500/30 text-red-300 p-3 mb-4 rounded text-center animate-popin">
                        <i class="fas fa-exclamation-circle mr-2"></i>{{ message|e }}
                    </div>
                {% endfor %}

                {# USERNAME CHANGE FORM #}
                {{ form_start(usernameChangeForm) }}
                    <div class="space-y-4">
                        {# USERNAME FIELD #}
                        <div>
                            <label class="block text-sm font-medium text-gray-300 mb-2">
                                <i class="fas fa-user mr-2 text-green-400"></i>New Username
                            </label>
                            {{ form_row(usernameChangeForm.username, {
                                'attr': {
                                    'class': 'w-full px-4 py-3 bg-gray-700/50 border border-gray-600/50 rounded text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-green-500/50 focus:border-green-500/50 transition-all duration-200',
                                    'placeholder': 'Enter new username'
                                }
                            }) }}
                        </div>

                        {# FORM SUBMIT BUTTON #}
                        <button type="submit" class="w-full px-4 py-3 bg-green-500/20 hover:bg-green-500/30 text-green-300 rounded transition-all duration-200 border border-green-500/30 font-medium">
                            <i class="fas fa-save mr-2"></i>Update Username
                        </button>
                    </div>
                {{ form_end(usernameChangeForm) }}
            </div>
        </div>
    </div>
</div>
{% endblock %}
