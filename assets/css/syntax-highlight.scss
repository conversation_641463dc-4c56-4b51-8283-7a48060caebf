/* syntax highlighting styles */
@import '../../node_modules/highlight.js/styles/stackoverflow-dark.css';

/* custom overrides for syntax highlighting */
.hljs {
    background: transparent;
    padding: 0.5rem;
    border-radius: 0.25rem;
    font-family: monospace;
    white-space: pre;
    word-break: normal;
}

/* ensure code blocks have proper spacing */
pre.file-content {
    margin: 0;
    padding: 0.3rem;
    overflow-x: auto;
    overflow-y: auto;
}

/* custom styles for general log highlighting */
.language-general-log {
    .hljs-meta {
        color: #e06c75;
    }
    .hljs-number {
        color: #d19a66;
    }
    .hljs-string {
        color: #98c379;
    }
    .hljs-keyword {
        color: #e5c07b;
    }
}
