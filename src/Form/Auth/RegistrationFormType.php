<?php

namespace App\Form\Auth;

use App\Entity\User;
use Symfony\Component\Form\AbstractType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\NotBlank;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Validator\Constraints\Sequentially;
use Symfony\Component\Form\Extension\Core\Type\PasswordType;
use Symfony\Component\Form\Extension\Core\Type\RepeatedType;

/**
 * Class RegistrationFormType
 *
 * The user registration form
 *
 * @extends AbstractType<User>
 *
 * @package App\Form\Auth
 */
class RegistrationFormType extends AbstractType
{
    /**
     * Build registration form
     *
     * @param FormBuilderInterface<User|null> $builder The form builder
     * @param array<string> $options The form options
     *
     * @return void
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $builder
            ->add('username', TextType::class, [
                'label' => false,
                'constraints' => new Sequentially([
                    new NotBlank(message: 'Please enter a username'),
                    new Length(
                        min: 3,
                        max: 155,
                        minMessage: 'Your username should be at least {{ limit }} characters',
                        maxMessage: 'Your username cannot be longer than {{ limit }} characters'
                    )
                ])
            ])
            ->add('password', RepeatedType::class, [
                'type' => PasswordType::class,
                'first_options' => [
                    'label' => false,
                    'constraints' => new Sequentially([
                        new NotBlank(message: 'Please enter a password'),
                        new Length(
                            min: 8,
                            max: 155,
                            minMessage: 'Your password should be at least {{ limit }} characters',
                            maxMessage: 'Your password cannot be longer than {{ limit }} characters'
                        )
                    ]),
                ],
                'second_options' => ['label' => false]
            ])
        ;
    }

    /**
     * Configure options for the registration form
     *
     * @param OptionsResolver $resolver
     *
     * @return void
     */
    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults([
            'data_class' => User::class
        ]);
    }
}
