<?xml version="1.0" encoding="UTF-8"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="vendor/squizlabs/php_codesniffer/phpcs.xsd">
    <arg name="extensions" value="php"/>
    <arg name="basepath" value="."/>
    <arg name="no-cache"/>
    <arg name="colors"/>

    <rule ref="PSR12">
        <properties>
            <property name="lineLimit" value="N"/>
            <property name="absoluteLineLimit" value="M"/>
        </properties>
    </rule>

    <file>src/</file>
    <file>tests/</file>
</ruleset>
