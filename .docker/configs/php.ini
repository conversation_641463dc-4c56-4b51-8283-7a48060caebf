[PHP]
engine = On
precision = 14

; ram limit
memory_limit = 1G

; resource limits
post_max_size = 512M
max_input_time = 120
max_execution_time = 60

; output buffering
output_buffering = 4096

; errors config
error_reporting = E_ALL
log_errors = On
display_errors = On
report_memleaks = On
display_startup_errors = On

; file upload limit
file_uploads = On
max_file_uploads = 100
upload_max_filesize = 512M

[Date]
date.timezone = "Europe/Prague"

[CLI Server]
cli_server.color = On

[Session]
session.save_handler = files
session.use_strict_mode = 0
session.use_cookies = 1
session.use_only_cookies = 1
session.name = PHPSESSID
session.auto_start = 0
session.cookie_lifetime = 0
session.cookie_path = /
session.serialize_handler = php
session.gc_probability = 0
session.gc_divisor = 1000
session.gc_maxlifetime = 1440
session.referer_check =
session.cache_limiter = nocache
session.cache_expire = 180
session.use_trans_sid = 0

[opcache]
opcache.enable=1
opcache.enable_cli=1
opcache.fast_shutdown=1
opcache.revalidate_freq=2
opcache.memory_consumption=128
opcache.interned_strings_buffer=8
opcache.max_accelerated_files=4000

[MySQLi]
mysqli.default_socket = /var/run/mysqld/mysqld.sock
