# /etc/apache2/sites-available/admin-suite.conf (site config example)
<VirtualHost *:80>
    # web server access point
    ServerName localhost

    # server admin contact
    <PERSON><PERSON><PERSON><PERSON> luka<PERSON>@becvar.xyz

    # public index path directory
    DocumentRoot /var/www/public

    # security policy headers
    # Header always set Permissions-Policy "geolocation=(self), microphone=()"
    # Header always set Content-Security-Policy "script-src 'self' 'unsafe-inline'; img-src 'self' data:;"
    # Header always append X-Frame-Options DENY
    # Header always set Referrer-Policy "no-referrer"

    # custom error pages
    ErrorDocument 400 /error?code=400
    ErrorDocument 401 /error?code=401    
    ErrorDocument 403 /error?code=403
    ErrorDocument 404 /error?code=404
    ErrorDocument 426 /error?code=426
    ErrorDocument 429 /error?code=429
    ErrorDocument 500 /error?code=500
    ErrorDocument 501 /error?code=501

    # site rules options
    <Directory /var/www/public>        
        # disable .htaccess
        AllowOverride None

        # allow access to public dir
        Require all granted

        # redirect all routes to main index file
        FallbackResource /index.php
    </Directory>

    # assets cache config
    # <IfModule mod_expires.c>
    #     ExpiresActive On
    #     ExpiresDefault "access plus 1 month"
    #
    #     ExpiresByType image/jpeg "access plus 1 month"
    #     ExpiresByType image/png "access plus 1 month"
    # </IfModule>

    # ssl config
    # SSLEngine on
    # SSLCertificateFile /services/others/keys/ssl/becvar.xyz.pem
    # SSLCertificateKeyFile /services/others/keys/ssl/becvar.xyz.key

	# logs config
	ErrorLog ${APACHE_LOG_DIR}/admin-suite-error.log
	CustomLog ${APACHE_LOG_DIR}/admin-suite-access.log combined
</VirtualHost>
